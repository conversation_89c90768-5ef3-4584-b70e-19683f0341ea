﻿using LibBaseModules.Helper;
using LibBaseModules.Helper.WinApiUtil;
using LibBaseModules.Helper.WinApiUtil.CommDataType;
using System;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;

namespace LibBaseModules.Communication
{
    /// <summary>
    /// 串口通信实现类
    /// </summary>
    public class ComBus : IDisposable
    {
        #region 字段属性

        private IntPtr _hPort = IntPtr.Zero;
        private IntPtr _closeEvent;
        private readonly string _closeEventName = "CloseEvent";
        private readonly DCB _dcb = new DCB();
        private DetailedPortSettings _portSettings;
        private readonly int _rxBufferSize = 1024 * 10;
        private readonly int _txBufferSize = 1024 * 10;
        private IntPtr _txOverlapped = IntPtr.Zero;
        private IntPtr _rxOverlapped = IntPtr.Zero;
        private CommEventFlags _eventFlags;
        private AutoResetEvent _rxEvent;
        private bool _needWaitRxCharEvent = true;
        private int _errorCount = 0;
        private readonly int _maxErrorCount = 0;
        private string _portName;
        private const int BufferSize = 1088;
        private readonly byte[] _buffer = new byte[BufferSize];
        /// <summary>
        /// 读数线程
        /// </summary>
        private Thread _thread;

        //发送触发事件
        private readonly AutoResetEvent _are = new AutoResetEvent(false);

        /// <summary>
        /// 重发次数
        /// </summary>
        private int _tryTimes = 2;

        /// <summary>
        /// 超时时间
        /// </summary>
        private int _timeout = 2000;

        /// <summary>
        /// 数据回应
        /// </summary>
        private byte[] _reDatas;

        /// <summary>
        /// 发送同步锁
        /// </summary>
        private readonly object _lockObj = new object();

        /// <summary>
        /// 当前是否连接
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 等待接收同步数据的标志
        /// </summary>
        private bool _isSyncSending;

        #endregion

        #region 构造

        /// <summary>
        /// 串口通信实现类
        /// </summary>
        public ComBus()
        {
            readBuffer = new byte[MAX_FRAME_SIZE * 2];
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 串口打开
        /// <param name="portName">串口名</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="dataBit">数据位</param>
        /// <param name="stopbits">停止位</param>
        /// <param name="parity">校验位</param>
        /// <param name="tryTimes">重发次数</param>
        /// <param name="timeout">超时时间</param>
        /// </summary>
        public void Open(string portName, int baudRate, int dataBit, StopBits stopbits, Parity parity, int tryTimes, int timeout)
        {
            if(!IsConnected)
            {
                _closeEvent = WinApiWrapper.CreateEvent(true, false, _closeEventName);
                _portSettings = new HandshakeNone();
                _portSettings.BasicSettings.BaudRate = (BaudRates)baudRate;
                _portSettings.BasicSettings.ByteSize = (byte)dataBit;
                _portSettings.BasicSettings.StopBits = stopbits;
                _portSettings.BasicSettings.Parity = parity;
                _portName = portName;
                _tryTimes = tryTimes;
                _timeout = timeout;

                bool flag = false;
                int waitTime = 100;

                for(int i = 1; i <= 3; i++)
                {
                    flag = _Open();
                    if(flag)
                    {
                        break;
                    }

                    Thread.Sleep(waitTime);
                    waitTime *= 2;
                }

                string info = flag ? "打开串口成功:" : "打开串口失败:";
                info += _portName;
                LogUtil.GetInstance().LogWrite(info);

                _thread = new Thread(ReadDataThread)
                {
                    Name = $"{_portName} ReadData Thread",
                    Priority = ThreadPriority.BelowNormal
                };
                _thread.Start();

                IsConnected = flag;
                if(!IsConnected)
                {
                    throw new Exception(info);
                }
            }
        }

        /// <summary>
        /// 串口关闭
        /// </summary>
        public void Close()
        {
            if(IsConnected)
            {
                bool flag = _Close();

                string info = flag ? "关闭串口成功:" : "关闭串口失败:";
                info += _portName;
                LogUtil.GetInstance().LogWrite(info);

                IsConnected = !flag;
            }
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data"></param>
        public byte[] SendData(byte[] data)
        {
            lock(_lockObj)
            {
                if(IsConnected)
                {
                    _reDatas = null;

                    // 尝试发送tryTimes次
                    for(int i = 0; i < _tryTimes; i++)
                    {
                        _isSyncSending = true;

                        if(!WinApiWrapper.WriteFile(_hPort, data, _txOverlapped) && Marshal.GetLastWin32Error() != (int)APIErrors.ERROR_IO_PENDING)
                        {
                            throw new TimeoutException("发送失败！");
                        }

                        LogUtil.GetInstance().LogWrite(text: $"[{_portName}]发送数据:{DataConverterHelper.BytesToString(data)}");

                        // 等待数据回应timeOut毫秒
                        if(_are.WaitOne(_timeout, true))
                        {
                            _isSyncSending = false;
                            return _reDatas;
                        }
                    }
                    _isSyncSending = false;
                    throw new TimeoutException("通信超时！");
                }
                else
                {
                    throw new Exception(@"串口未打开!");
                }
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 关闭串口
        /// </summary>
        public void Dispose()
        {
            Close();
        }

        #endregion

        #region 私有方法

        #region 开关

        private bool _Open()
        {
            OVERLAPPED o = new OVERLAPPED();
            _txOverlapped = WinApiWrapper.LocalAlloc(0x40, Marshal.SizeOf(o));
            o.Offset = 0;
            o.OffsetHigh = 0;
            o.hEvent = IntPtr.Zero;
            Marshal.StructureToPtr(o, _txOverlapped, true);

            _hPort = WinApiWrapper.CreateFile(GetPortName());

            if(_hPort == (IntPtr)WinApiWrapper.INVALID_HANDLE_VALUE)
            {
                return false;
            }

            // set queue sizes
            WinApiWrapper.SetupComm(_hPort, _rxBufferSize, _txBufferSize);
            TransferPortSettings2DCB();
            WinApiWrapper.SetCommState(_hPort, _dcb);

            // set the Comm timeouts
            CommTimeouts ct = new CommTimeouts();

            // reading we'll return immediately
            // this doesn't seem to work as documented
            ct.ReadIntervalTimeout = uint.MaxValue; // this = 0xffffffff
            ct.ReadTotalTimeoutConstant = 0;
            ct.ReadTotalTimeoutMultiplier = 0;

            // writing we'll give 5 seconds
            ct.WriteTotalTimeoutConstant = 5000;
            ct.WriteTotalTimeoutMultiplier = 0;

            WinApiWrapper.SetCommTimeouts(_hPort, ct);

            _eventFlags = new CommEventFlags();
            _rxEvent = new AutoResetEvent(false);


            WinApiWrapper.SetCommMask(_hPort, CommEventFlags.ALLPC);
            // set up the overlapped tx IO
            // AutoResetEvent are = new AutoResetEvent(false);
            o = new OVERLAPPED();
            _rxOverlapped = WinApiWrapper.LocalAlloc(0x40, Marshal.SizeOf(o));
            o.Offset = 0;
            o.OffsetHigh = 0;
            o.hEvent = _rxEvent.Handle;
            Marshal.StructureToPtr(o, _rxOverlapped, true);

            return true;
        }

        private bool _Close()
        {

            if(_txOverlapped != IntPtr.Zero)
            {
                WinApiWrapper.LocalFree(_txOverlapped);
                _txOverlapped = IntPtr.Zero;
            }

            if(WinApiWrapper.CloseHandle(_hPort))
            {
                WinApiWrapper.SetEvent(_closeEvent);
                _hPort = (IntPtr)WinApiWrapper.INVALID_HANDLE_VALUE;
                WinApiWrapper.SetEvent(_closeEvent);
                return true;
            }

            return false;
        }

        private string GetPortName()
        {
            string port = _portName;

            int.TryParse(port?.Substring(3), out int commNum);

            if(commNum >= 10 && commNum <= 255)
            {
                return @"\\.\" + port;
            }
            else
            {
                return port + ":";
            }
        }

        // transfer the port settings to a DCB structure
        private void TransferPortSettings2DCB()
        {
            _dcb.BaudRate = (uint)_portSettings.BasicSettings.BaudRate;
            _dcb.ByteSize = _portSettings.BasicSettings.ByteSize;
            _dcb.EofChar = (sbyte)_portSettings.EOFChar;
            _dcb.ErrorChar = (sbyte)_portSettings.ErrorChar;
            _dcb.EvtChar = (sbyte)_portSettings.EVTChar;
            _dcb.fAbortOnError = _portSettings.AbortOnError;
            _dcb.fBinary = true;
            _dcb.fDsrSensitivity = _portSettings.DSRSensitive;
            _dcb.fDtrControl = (DCB.DtrControlFlags)_portSettings.DTRControl;
            _dcb.fErrorChar = _portSettings.ReplaceErrorChar;
            _dcb.fInX = _portSettings.InX;
            _dcb.fNull = _portSettings.DiscardNulls;
            _dcb.fOutX = _portSettings.OutX;
            _dcb.fOutxCtsFlow = _portSettings.OutCTS;
            _dcb.fOutxDsrFlow = _portSettings.OutDSR;
            _dcb.fParity = (_portSettings.BasicSettings.Parity == Parity.None) ? false : true;
            _dcb.fRtsControl = (DCB.RtsControlFlags)_portSettings.RTSControl;
            _dcb.fTXContinueOnXoff = _portSettings.TxContinueOnXOff;
            _dcb.Parity = (byte)_portSettings.BasicSettings.Parity;
            _dcb.StopBits = (byte)_portSettings.BasicSettings.StopBits;
            _dcb.XoffChar = (sbyte)_portSettings.XoffChar;
            _dcb.XonChar = (sbyte)_portSettings.XonChar;
            _dcb.XonLim = _dcb.XoffLim = (ushort)(_rxBufferSize / 10);
        }

        #endregion

        #region 数据接收

        private bool Read(byte[] buf, int count, ref int bytesread)
        {

            if(!_needWaitRxCharEvent || (_needWaitRxCharEvent && WaitRxCharEvent()))
            {
                // make sure the port handle is valid
                if(_hPort == (IntPtr)WinApiWrapper.INVALID_HANDLE_VALUE)
                {
                    bytesread = 0;
                    return true;
                }

                // data came in, put it in the buffer and set the event

                if(!WinApiWrapper.ReadFile(_hPort, buf, count, ref bytesread, _rxOverlapped))
                {
                    string error = String.Format("ReadFile Failed: {0}", Marshal.GetLastWin32Error());
                    throw new Exception(error);
                }

                _needWaitRxCharEvent = (bytesread <= 0);

                return true;
            }
            bytesread = 0;
            return false;
        }

        private void ReadDataThread()
        {
            _errorCount = 0;
            while(IsConnected)
            {
                try
                {
                    int count = 0;
                    Read(_buffer, BufferSize, ref count);
                    if(count > 0)
                    {
                        byte[] tempData = new byte[count];
                        Buffer.BlockCopy(_buffer, 0, tempData, 0, count);

                        StartReceive(tempData);
                    }
                    else
                    {
                        _errorCount++;
                    }
                }
                catch(Exception e)
                {
                    string error = $"物理链路:[{_portName}] 发生读取异常:{e.Message}";
                    _errorCount++;
                    CheckErrorCount(error);
                }
            }
        }

        private void CheckErrorCount(string message)
        {
            if(_maxErrorCount > 0 && _errorCount >= _maxErrorCount)
            {
                Close();
                LogUtil.GetInstance().LogWrite(message);
            }
        }

        private bool WaitRxCharEvent()
        {
            // wait for a Comm event
            if(!WinApiWrapper.WaitCommEvent(_hPort, ref _eventFlags))
            {
                int e = Marshal.GetLastWin32Error();

                if(e == (int)APIErrors.ERROR_IO_PENDING)
                {
                    // IO pending so just wait and try again
                    _rxEvent.WaitOne();
                    Thread.Sleep(0);
                    return false;
                }

                if(e == (int)APIErrors.ERROR_INVALID_HANDLE)
                {
                    // Calling Port.Close() causes hPort to become invalid
                    // Since Thread.Abort() is unsupported in the CF, we must
                    // accept that calling Close will throw an error here.

                    // Close signals the _closeEvent, so wait on it
                    // We wait 1 second, though Close should happen much sooner
                    int eventResult = WinApiWrapper.WaitForSingleObject(_closeEvent, 1000);

                    if(eventResult == (int)APIConstants.WAIT_OBJECT_0)
                    {
                        // the event was set so close was called
                        _hPort = (IntPtr)WinApiWrapper.INVALID_HANDLE_VALUE;
                        throw new Exception("port closed");
                    }
                }

                // WaitCommEvent failed
                // 995 means an exit was requested (thread killed)
                if(e == 995)
                {
                    //throw new System.Threading.ThreadInterruptedException("thread killed");
                }
                else
                {
                    string error = String.Format("Wait Failed: {0}", e);
                    throw new Exception(error);
                }
            }

            WinApiWrapper.SetCommMask(_hPort, CommEventFlags.ALLPC);

            // check the event for errors

            #region >>>> error checking <<<<

            if(((uint)_eventFlags & (uint)CommEventFlags.ERR) != 0)
            {
                CommErrorFlags errorFlags = new CommErrorFlags();
                CommStat commStat = new CommStat();

                // get the error status
                if(!WinApiWrapper.ClearCommError(_hPort, ref errorFlags, commStat))
                {
                    // ClearCommError failed!
                    string error = String.Format("ClearCommError Failed: {0}", Marshal.GetLastWin32Error());
                    throw new Exception(error);
                }

                if(((uint)errorFlags & (uint)CommErrorFlags.BREAK) != 0)
                {
                    // BREAK can set an error, so make sure the BREAK bit is set an continue
                    _eventFlags |= CommEventFlags.BREAK;
                }
                else
                {
                    // we have an error. Build a meaningful string and throw an exception
                    StringBuilder s = new StringBuilder("UART Error: ", 80);
                    if((errorFlags & CommErrorFlags.FRAME) != 0)
                    {
                        s = s.Append("Framing,");
                    }
                    if((errorFlags & CommErrorFlags.IOE) != 0)
                    {
                        s = s.Append("IO,");
                    }
                    if((errorFlags & CommErrorFlags.OVERRUN) != 0)
                    {
                        s = s.Append("Overrun,");
                    }
                    if((errorFlags & CommErrorFlags.RXOVER) != 0)
                    {
                        s = s.Append("Receive Overflow,");
                    }
                    if((errorFlags & CommErrorFlags.RXPARITY) != 0)
                    {
                        s = s.Append("Parity,");
                    }
                    if((errorFlags & CommErrorFlags.TXFULL) != 0)
                    {
                        s = s.Append("Transmit Overflow,");
                    }

                    // no known bits are set
                    if(s.Length == 12)
                    {
                        s = s.Append("Unknown");
                    }

                    return false;
                }
            } // if(((uint)_eventFlags & (uint)CommEventFlags.ERR) != 0)

            #endregion

            return ((uint)_eventFlags & (uint)CommEventFlags.RXCHAR) != 0;
        }

        //该协议适用于没有明显帧头帧尾的数据传输
        //使用帧间隔400ms判断数据传输完成,对接收到的数据组包向上层传
        private uint waitTime = 600;
        // 协议解析常量及模版变量

        /// <summary>默认编码 ASCII</summary>
        protected Encoding encoding = Encoding.UTF8;

        /// <summary>最小数据帧长度</summary>
        protected const int MIN_FRAME_SIZE = 1;

        /// <summary>最大数据帧长度</summary>
        protected const int MAX_FRAME_SIZE = 8192;

        /// <summary>读取数据的缓存</summary>
        protected byte[] frameBuffer;

        /// <summary>记录每次读取数据长度</summary>
        protected int readedBytes = 0;

        /// <summary>当前已经接收数据长度</summary>
        protected int recevicedDataSize = 0;

        /// <summary>帧头在tempReadBuffer中的位置</summary>
        protected int headIndex = -1;

        private byte[] readBuffer;

        //数据发送线程
        private Thread dataReceiveThread = null;

        //数据接收触发事件
        private IntPtr receiveEvent = WinApiWrapper.CreateEvent(false, false, "dcon_receive_event");

        public void StartReceive(byte[] receiveData)
        {
            readedBytes = receiveData.Length;

            if((recevicedDataSize + readedBytes) > readBuffer.Length)
            {
                recevicedDataSize = 0;
                headIndex = -1;
            }

            GetDataReceiveThread();
            Buffer.BlockCopy(receiveData, 0, readBuffer, recevicedDataSize, receiveData.Length);
            recevicedDataSize += readedBytes;
            WinApiWrapper.SetEvent(receiveEvent);
        }


        private void GetDataReceiveThread()
        {
            if(dataReceiveThread == null)
            {
                dataReceiveThread = new Thread(DataReceiveThreadFunc)
                {
                    Name = $"[{_portName}]协议解析线程"
                };

                dataReceiveThread.Start();
            }
        }

        private void DataReceiveThreadFunc()
        {
            while(IsConnected)
            {
                uint waitValue = (uint)WinApiWrapper.WaitForSingleObject(receiveEvent, (uint)waitTime);

                //Event触发(有新数据)
                if(waitValue == (uint)APIConstants.WAIT_OBJECT_0)
                {
                    continue;
                }

                //timeout发生
                else if(waitValue == (uint)APIConstants.WAIT_TIMEOUT)
                {
                    byte[] data = null;
                    lock(this)
                    {
                        try
                        {
                            data = new byte[recevicedDataSize];
                            Buffer.BlockCopy(readBuffer, 0, data, 0, recevicedDataSize);
                            recevicedDataSize = 0;
                        }
                        catch(Exception ex)
                        {
                            //AqmsLogHelper.GetInstance().TraceExceptionMsg("ModPot:" + ex.Message + "/r/n" + "recevicedDataSize = " + recevicedDataSize.ToString());
                            recevicedDataSize = 0;
                            continue;
                        }
                    }
                    if(data.Length < 1)
                    {
                        continue;
                    }

                    if(_isSyncSending)
                    {
                        _reDatas = data;
                        _are.Set();
                    }

                    LogUtil.GetInstance().LogWrite($"[{_portName}]接收数据:{DataConverterHelper.BytesToString(_reDatas)}");
                }
                else
                {
                    //其它错误
                    continue;
                }
            }

            dataReceiveThread = null;
        }

        #endregion

        #endregion
    }
}
