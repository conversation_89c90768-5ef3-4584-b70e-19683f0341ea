using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Sunny.UI;
using System;
using System.Data;
using System.IO;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BorderStyle = NPOI.SS.UserModel.BorderStyle;
using DataTable = System.Data.DataTable;
using HorizontalAlignment = NPOI.SS.UserModel.HorizontalAlignment;

namespace Fpi.Util
{
    public static class FileExportHelper
    {
        #region ListView

        /// <summary>
        /// 将ListView输出到Excel文件中
        /// </summary>
        /// <param name="lvData"></param>
        /// <param name="fileName"></param>
        /// <param name="showSchedule">是否显示导出进度</param>
        /// <param name="drawBorder">是否绘制边框</param>
        /// <returns></returns>
        public static void SaveListViewToExcelFile(this Form owner, ListView lvData, string fileName, bool showSchedule = true, bool drawBorder = true)
        {
            FileStream file = null;
            try
            {
                var rowConut = lvData.Items.Count;

                if(showSchedule)
                {
                    // 打开进度条界面
                    UIFormServiceHelper.ShowStatusForm(owner, rowConut, "数据处理中，请稍候...");
                }

                // 创建workBook，不指定参数，表示创建一个新的工作本
                XSSFWorkbook xssfWorkbook = new XSSFWorkbook();

                // 默认单元格样式
                ICellStyle defaultCellStyle = xssfWorkbook.CreateCellStyle();
                defaultCellStyle.Alignment = HorizontalAlignment.Center;
                if(drawBorder)
                {
                    defaultCellStyle.BorderBottom = BorderStyle.Thin;
                    defaultCellStyle.BorderLeft = BorderStyle.Thin;
                    defaultCellStyle.BorderRight = BorderStyle.Thin;
                    defaultCellStyle.BorderTop = BorderStyle.Thin;
                    defaultCellStyle.BottomBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.LeftBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.RightBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.TopBorderColor = HSSFColor.Black.Index;
                }

                // 分表计数器
                int sheetCount = 1;
                ISheet sheet = xssfWorkbook.CreateSheet("Sheet" + sheetCount);
                // 标题行
                IRow headRow = sheet.CreateRow(0);
                for(int j = 0; j < lvData.Columns.Count; j++)
                {
                    ICell cell = headRow.CreateCell(j);
                    cell.CellStyle = defaultCellStyle;
                    cell.SetCellValue(lvData.Columns[j].Text);
                }

                // 行数计数器
                int currentRowCount = 1;
                // 遍历数据区
                for(int i = 0; i < rowConut; i++)
                {
                    // 分表
                    if(currentRowCount == 64000)
                    {
                        currentRowCount = 1;
                        sheetCount++;
                        sheet = xssfWorkbook.CreateSheet("Sheet" + sheetCount);
                        // 标题行
                        headRow = sheet.CreateRow(0);
                        for(int j = 0; j < lvData.Columns.Count; j++)
                        {
                            ICell cell = headRow.CreateCell(j);
                            cell.CellStyle = defaultCellStyle;
                            cell.SetCellValue(lvData.Columns[j].Text);
                        }
                    }

                    // 内容行
                    IRow row = sheet.CreateRow(currentRowCount);

                    ListViewItem item = lvData.Items[i];
                    for(int y = 0; y < lvData.Columns.Count; y++)
                    {
                        ICell cell = row.CreateCell(y);
                        cell.CellStyle = defaultCellStyle;
                        cell.SetCellValue(item.SubItems[y]?.Text);
                    }

                    if(showSchedule)
                    {
                        UIFormServiceHelper.SetStatusFormDescription(owner, $"数据处理中[{i}/{rowConut}]......");
                        UIFormServiceHelper.SetStatusFormStepIt(owner, i);
                    }

                    currentRowCount++;
                }

                // 遍历所有表的所有列，自适应宽度
                for(int i = 0; i < xssfWorkbook.NumberOfSheets; i++)
                {
                    var she = xssfWorkbook.GetSheetAt(i);
                    var ro = she.GetRow(0);
                    if(ro != null)
                    {
                        for(int j = 0; j < ro.LastCellNum; j++)
                        {
                            she.AutoSizeColumn(j);
                        }
                    }
                }

                file = new FileStream(fileName, FileMode.Create);
                xssfWorkbook.Write(file);
            }
            catch(Exception e)
            {
                throw new Exception("保存文件出错：" + e.Message);
            }
            finally
            {
                file?.Close();
                if(showSchedule)
                {
                    // 线程切换，防止最终进度界面无法关闭
                    Thread.Sleep(50);
                    // 隐藏进度条界面
                    UIFormServiceHelper.HideStatusForm(owner);
                }
            }
        }

        /// <summary>
        /// 将ListView输出到Txt文件中
        /// </summary>
        /// <param name="listView"></param>
        /// <param name="fileName"></param>
        /// <param name="showSchedule">是否显示导出进度</param>
        /// <returns></returns>
        public static void SaveListViewToTxtFile(this Form owner, ListView listView, string fileName, bool showSchedule = true)
        {
            TextWriter textWriter = null;
            try
            {
                var rowConut = listView.Items.Count;

                if(showSchedule)
                {
                    // 打开进度条界面
                    UIFormServiceHelper.ShowStatusForm(owner, rowConut, "数据处理中，请稍候...");
                }

                if(File.Exists(fileName))
                {
                    File.SetAttributes(fileName, FileAttributes.Normal);
                    File.Delete(fileName);
                }

                textWriter = File.CreateText(fileName);
                StringBuilder sb = new StringBuilder();

                // 标题栏
                // 首列 时间区加宽
                if(listView.Columns.Count > 0)
                {
                    sb.Append(listView.Columns[0].Text.PadRight(30, ' '));
                }
                for(int i = 1; i < listView.Columns.Count; i++)
                {
                    sb.Append(listView.Columns[i].Text.PadRight(10, ' '));
                }
                sb.Append("\t");

                textWriter.WriteLine(sb.ToString());
                textWriter.Flush();
                sb.Remove(0, sb.Length);

                // 数据区
                for(int i = 0; i < listView.Items.Count; i++)
                {
                    // 首列 时间区加宽
                    var dtRow = listView.Items[i];
                    if(dtRow.SubItems.Count > 0)
                    {
                        sb.Append(dtRow.SubItems[0].Text.PadRight(35, ' '));
                    }
                    for(int j = 1; j < dtRow.SubItems.Count; j++)
                    {
                        sb.Append(dtRow.SubItems[j].Text.PadRight(15, ' '));
                    }
                    sb.Append("\t");

                    textWriter.WriteLine(sb.ToString());
                    textWriter.Flush();
                    sb.Remove(0, sb.Length);

                    if(showSchedule)
                    {
                        UIFormServiceHelper.SetStatusFormDescription(owner, $"数据处理中[{i}/{rowConut}]......");
                        UIFormServiceHelper.SetStatusFormStepIt(owner, i);
                    }
                }
            }
            finally
            {
                textWriter?.Close();
                if(showSchedule)
                {
                    // 线程切换，防止最终进度界面无法关闭
                    Thread.Sleep(50);
                    // 隐藏进度条界面
                    UIFormServiceHelper.HideStatusForm(owner);
                }
            }
        }

        #endregion

        #region DataGridView

        /// <summary>
        /// 将DataGridView输出到Excel文件中
        /// </summary>
        /// <param name="dgvData"></param>
        /// <param name="fileName"></param>
        /// <param name="saveRowHeader">是否保存行标题</param>
        /// <param name="showSchedule">是否显示导出进度</param>
        /// <param name="drawBorder">是否绘制边框</param>
        public static void SaveDataGridViewToExcelFile(this Form owner, UIDataGridView dgvData,
            string fileName, bool saveRowHeader = false, bool showSchedule = true, bool drawBorder = true)
        {
            FileStream file = null;
            try
            {
                var rowConut = dgvData.Rows.Count;

                if(showSchedule)
                {
                    // 打开进度条界面
                    UIFormServiceHelper.ShowStatusForm(owner, rowConut, "数据处理中，请稍候...");
                }

                // 创建workBook，不指定参数，表示创建一个新的工作本
                XSSFWorkbook xssfWorkbook = new XSSFWorkbook();

                // 默认单元格样式
                ICellStyle defaultCellStyle = xssfWorkbook.CreateCellStyle();
                defaultCellStyle.Alignment = HorizontalAlignment.Center;
                if(drawBorder)
                {
                    defaultCellStyle.BorderBottom = BorderStyle.Thin;
                    defaultCellStyle.BorderLeft = BorderStyle.Thin;
                    defaultCellStyle.BorderRight = BorderStyle.Thin;
                    defaultCellStyle.BorderTop = BorderStyle.Thin;
                    defaultCellStyle.BottomBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.LeftBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.RightBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.TopBorderColor = HSSFColor.Black.Index;
                }

                // 分表计数器
                int sheetCount = 1;
                int startColumn = 0;
                ISheet sheet = xssfWorkbook.CreateSheet("Sheet" + sheetCount);
                // 标题行
                IRow headRow = sheet.CreateRow(0);
                // 保存行标题
                if(saveRowHeader && dgvData.RowHeadersVisible)
                {
                    ICell cell = headRow.CreateCell(0);
                    cell.CellStyle = defaultCellStyle;
                    startColumn = 1;
                }
                for(int j = 0; j < dgvData.Columns.Count; j++)
                {
                    ICell cell = headRow.CreateCell(j + startColumn);
                    cell.CellStyle = defaultCellStyle;
                    cell.SetCellValue(dgvData.Columns[j].HeaderText);
                }

                // 行数计数器
                int currentRowCount = 1;
                // 遍历数据区
                for(int i = 0; i < rowConut; i++)
                {
                    // 分表
                    if(currentRowCount == 64000)
                    {
                        currentRowCount = 1;
                        sheetCount++;
                        sheet = xssfWorkbook.CreateSheet("Sheet" + sheetCount);
                        // 标题行
                        headRow = sheet.CreateRow(0);
                        // 保存行标题
                        if(saveRowHeader && dgvData.RowHeadersVisible)
                        {
                            ICell cell = headRow.CreateCell(0);
                            cell.CellStyle = defaultCellStyle;
                            startColumn = 1;
                        }

                        for(int j = 0; j < dgvData.Columns.Count; j++)
                        {
                            ICell cell = headRow.CreateCell(j + startColumn);
                            cell.CellStyle = defaultCellStyle;
                            cell.SetCellValue(dgvData.Columns[j].HeaderText);
                        }
                    }

                    // 内容行
                    IRow row = sheet.CreateRow(currentRowCount);
                    DataGridViewRow item = dgvData.Rows[i];
                    // 保存行标题
                    if(saveRowHeader && dgvData.RowHeadersVisible)
                    {
                        ICell cell = row.CreateCell(0);
                        cell.CellStyle = defaultCellStyle;
                        cell.SetCellValue(dgvData.Rows[i].HeaderCell.Value?.ToString());
                    }
                    for(int y = 0; y < dgvData.Columns.Count; y++)
                    {
                        ICell cell = row.CreateCell(y + startColumn);
                        cell.CellStyle = defaultCellStyle;
                        cell.SetCellValue(item.Cells[y].Value?.ToString());
                    }

                    if(showSchedule)
                    {
                        UIFormServiceHelper.SetStatusFormDescription(owner, $"数据处理中[{i}/{rowConut}]......");
                        UIFormServiceHelper.SetStatusFormStepIt(owner, i);
                    }

                    currentRowCount++;
                }

                // 遍历所有表的所有列，自适应宽度
                for(int i = 0; i < xssfWorkbook.NumberOfSheets; i++)
                {
                    var she = xssfWorkbook.GetSheetAt(i);
                    var ro = she.GetRow(0);
                    if(ro != null)
                    {
                        for(int j = 0; j < ro.LastCellNum; j++)
                        {
                            she.AutoSizeColumn(j);
                        }
                    }
                }

                file = new FileStream(fileName, FileMode.Create);
                xssfWorkbook.Write(file);
            }
            catch(Exception e)
            {
                throw new Exception("保存文件出错：" + e.Message);
            }
            finally
            {
                file?.Close();
                if(showSchedule)
                {
                    // 线程切换，防止最终进度界面无法关闭
                    Thread.Sleep(50);
                    // 隐藏进度条界面
                    UIFormServiceHelper.HideStatusForm(owner);
                }
            }
        }

        /// <summary>
        /// 将DataGridView输出到Txt文件中
        /// </summary>
        /// <param name="dgvData"></param>
        /// <param name="fileName"></param>
        /// <param name="showSchedule">是否显示导出进度</param>
        /// <returns></returns>
        public static void SaveDataGridViewToTxtFile(this Form owner, UIDataGridView dgvData, string fileName, bool showSchedule = true)
        {
            TextWriter textWriter = null;
            try
            {
                var rowConut = dgvData.Rows.Count;

                if(showSchedule)
                {
                    // 打开进度条界面
                    UIFormServiceHelper.ShowStatusForm(owner, rowConut, "数据处理中，请稍候...");
                }

                if(File.Exists(fileName))
                {
                    File.SetAttributes(fileName, FileAttributes.Normal);
                    File.Delete(fileName);
                }

                textWriter = File.CreateText(fileName);
                StringBuilder sb = new StringBuilder();

                // 标题栏
                // 首列 时间区加宽
                if(dgvData.Columns.Count > 0)
                {
                    sb.Append(dgvData.Columns[0].HeaderText.PadRight(30, ' '));
                }
                for(int i = 1; i < dgvData.Columns.Count; i++)
                {
                    sb.Append(dgvData.Columns[i].HeaderText.PadRight(10, ' '));
                }
                sb.Append("\t");

                textWriter.WriteLine(sb.ToString());
                textWriter.Flush();
                sb.Remove(0, sb.Length);

                // 数据区
                for(int i = 0; i < rowConut; i++)
                {
                    var dtRow = dgvData.Rows[i];
                    // 首列 时间区加宽
                    if(dtRow.Cells.Count > 0)
                    {
                        sb.Append(dtRow.Cells[0].Value.ToString().PadRight(35, ' '));
                    }
                    for(int j = 1; j < dtRow.Cells.Count; j++)
                    {
                        sb.Append(dtRow.Cells[j].Value.ToString().PadRight(15, ' '));
                    }
                    sb.Append("\t");

                    textWriter.WriteLine(sb.ToString());
                    textWriter.Flush();
                    sb.Remove(0, sb.Length);

                    if(showSchedule)
                    {
                        UIFormServiceHelper.SetStatusFormDescription(owner, $"数据处理中[{i}/{rowConut}]......");
                        UIFormServiceHelper.SetStatusFormStepIt(owner, i);
                    }
                }
            }
            finally
            {
                textWriter?.Close();
                if(showSchedule)
                {
                    // 线程切换，防止最终进度界面无法关闭
                    Thread.Sleep(50);
                    // 隐藏进度条界面
                    UIFormServiceHelper.HideStatusForm(owner);
                }
            }
        }

        #endregion

        #region DataTable

        /// <summary>
        /// 将DataTable输出到Excel文件中
        /// </summary>
        /// <param name="dtData"></param>
        /// <param name="fileName"></param>
        /// <param name="showSchedule">是否显示导出进度</param>
        /// <param name="drawBorder">是否绘制边框</param>
        /// <returns></returns>
        public static void SaveDataTableToExcelFile(this Form owner, DataTable dtData, string fileName, bool showSchedule = true, bool drawBorder = true)
        {
            FileStream file = null;
            try
            {
                var rowConut = dtData.Rows.Count;

                if(showSchedule)
                {
                    // 打开进度条界面
                    UIFormServiceHelper.ShowStatusForm(owner, rowConut, "数据处理中，请稍候...");
                }

                // 创建workBook，不指定参数，表示创建一个新的工作本
                XSSFWorkbook xssfWorkbook = new XSSFWorkbook();

                // 默认单元格样式
                ICellStyle defaultCellStyle = xssfWorkbook.CreateCellStyle();
                defaultCellStyle.Alignment = HorizontalAlignment.Center;
                if(drawBorder)
                {
                    defaultCellStyle.BorderBottom = BorderStyle.Thin;
                    defaultCellStyle.BorderLeft = BorderStyle.Thin;
                    defaultCellStyle.BorderRight = BorderStyle.Thin;
                    defaultCellStyle.BorderTop = BorderStyle.Thin;
                    defaultCellStyle.BottomBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.LeftBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.RightBorderColor = HSSFColor.Black.Index;
                    defaultCellStyle.TopBorderColor = HSSFColor.Black.Index;
                }

                // 分表计数器
                int sheetCount = 1;
                ISheet sheet = xssfWorkbook.CreateSheet("Sheet" + sheetCount);
                // 标题行
                IRow headRow = sheet.CreateRow(0);
                for(int j = 0; j < dtData.Columns.Count; j++)
                {
                    ICell cell = headRow.CreateCell(j);
                    cell.CellStyle = defaultCellStyle;
                    cell.SetCellValue(dtData.Columns[j].Caption);
                }

                // 行数计数器
                int currentRowCount = 1;
                // 遍历数据区
                for(int i = 0; i < rowConut; i++)
                {
                    // 分表
                    if(currentRowCount == 64000)
                    {
                        currentRowCount = 1;
                        sheetCount++;
                        sheet = xssfWorkbook.CreateSheet("Sheet" + sheetCount);
                        // 标题行
                        headRow = sheet.CreateRow(0);
                        for(int j = 0; j < dtData.Columns.Count; j++)
                        {
                            ICell cell = headRow.CreateCell(j);
                            cell.CellStyle = defaultCellStyle;
                            cell.SetCellValue(dtData.Columns[j].Caption);
                        }
                    }

                    // 内容行
                    IRow row = sheet.CreateRow(currentRowCount);
                    DataRow dtRow = dtData.Rows[i];
                    for(int y = 0; y < dtData.Columns.Count; y++)
                    {
                        ICell cell = row.CreateCell(y);
                        cell.CellStyle = defaultCellStyle;
                        cell.SetCellValue(dtRow[y]?.ToString());
                    }

                    if(showSchedule)
                    {
                        UIFormServiceHelper.SetStatusFormDescription(owner, $"数据处理中[{i}/{rowConut}]......");
                        UIFormServiceHelper.SetStatusFormStepIt(owner, i);
                    }

                    currentRowCount++;
                }

                // 遍历所有表的所有列，自适应宽度
                for(int i = 0; i < xssfWorkbook.NumberOfSheets; i++)
                {
                    var she = xssfWorkbook.GetSheetAt(i);
                    var ro = she.GetRow(0);
                    if(ro != null)
                    {
                        for(int j = 0; j < ro.LastCellNum; j++)
                        {
                            she.AutoSizeColumn(j);
                        }
                    }
                }

                file = new FileStream(fileName, FileMode.Create);
                xssfWorkbook.Write(file);
            }
            catch(Exception e)
            {
                throw new Exception("保存文件出错：" + e.Message);
            }
            finally
            {
                file?.Close();
                if(showSchedule)
                {
                    // 线程切换，防止最终进度界面无法关闭
                    Thread.Sleep(50);
                    // 隐藏进度条界面
                    UIFormServiceHelper.HideStatusForm(owner);
                }
            }
        }

        /// <summary>
        /// 将DataTable输出到Txt文件中
        /// </summary>
        /// <param name="dtData"></param>
        /// <param name="fileName"></param>
        /// <param name="showSchedule">是否显示导出进度</param>
        /// <returns></returns>
        public static void SaveDataTableToTxtFile(this Form owner, DataTable dtData, string fileName, bool showSchedule = true)
        {
            TextWriter textWriter = null;
            try
            {
                var rowConut = dtData.Rows.Count;

                if(showSchedule)
                {
                    // 打开进度条界面
                    UIFormServiceHelper.ShowStatusForm(owner, rowConut, "数据处理中，请稍候...");
                }

                if(File.Exists(fileName))
                {
                    File.SetAttributes(fileName, FileAttributes.Normal);
                    File.Delete(fileName);
                }

                textWriter = File.CreateText(fileName);
                StringBuilder sb = new StringBuilder();
                // 标题栏
                // 首列 时间区加宽
                if(dtData.Columns.Count > 0)
                {
                    sb.Append(dtData.Columns[0].Caption.PadRight(30, ' '));
                }
                for(int i = 1; i < dtData.Columns.Count; i++)
                {
                    sb.Append(dtData.Columns[i].Caption.PadRight(10, ' '));
                }
                sb.Append("\t");

                textWriter.WriteLine(sb.ToString());
                textWriter.Flush();
                sb.Remove(0, sb.Length);

                // 数据区
                for(int i = 0; i < rowConut; i++)
                {
                    DataRow dtRow = dtData.Rows[i];
                    // 首列 时间区加宽
                    if(dtRow.ItemArray.Length > 0)
                    {
                        sb.Append(dtRow[0].ToString().PadRight(35, ' '));
                    }
                    for(int j = 1; j < dtRow.ItemArray.Length; j++)
                    {
                        sb.Append(dtRow[j].ToString().PadRight(15, ' '));
                    }
                    sb.Append("\t");

                    textWriter.WriteLine(sb.ToString());
                    textWriter.Flush();
                    sb.Remove(0, sb.Length);

                    if(showSchedule)
                    {
                        UIFormServiceHelper.SetStatusFormDescription(owner, $"数据处理中[{i}/{rowConut}]......");
                        UIFormServiceHelper.SetStatusFormStepIt(owner, i);
                    }
                }
            }
            finally
            {
                textWriter?.Close();
                if(showSchedule)
                {
                    // 线程切换，防止最终进度界面无法关闭
                    Thread.Sleep(50);
                    // 隐藏进度条界面
                    UIFormServiceHelper.HideStatusForm(owner);
                }
            }
        }

        #endregion

        #region TXT

        /// <summary>
        /// 保存txt文件至指定路径
        /// </summary>
        /// <param name="str"></param>
        /// <param name="fileName"></param>
        public static void SaveStrToTxtFile(string str, string fileName)
        {
            TextWriter textWriter = null;
            try
            {
                if(File.Exists(fileName))
                {
                    File.SetAttributes(fileName, FileAttributes.Normal);
                    File.Delete(fileName);
                }

                textWriter = File.CreateText(fileName);
                File.SetAttributes(fileName, FileAttributes.ReadOnly);
                textWriter.WriteLine(str);
                textWriter.Flush();
            }
            catch(Exception e)
            {
                throw new Exception("保存文件失败！----" + e.Message);
            }
            finally
            {
                if(textWriter != null)
                {
                    textWriter.Close();
                }
            }
        }

        #endregion
    }
}