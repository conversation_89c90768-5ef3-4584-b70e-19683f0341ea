﻿using Newtonsoft.Json;
using System;

namespace LibBaseModules.Helper
{
    public static class SerializeHelper
    {
        /// <summary>
        /// 深拷贝
        /// </summary>
        /// <param name="originData">源数据</param>
        /// <returns>深拷贝后的数据</returns>
        public static T DeepCopyData<T>(T originData) where T : class
        {
            if(originData == null) return null;
            try
            {
                // 使用JSON序列化进行深拷贝
                string json = JsonConvert.SerializeObject(originData, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    TypeNameHandling = TypeNameHandling.Auto
                });
                return JsonConvert.DeserializeObject<T>(json, new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto
                });
            }
            catch(Exception ex)
            {
                return null;
            }
        }
    }
}
