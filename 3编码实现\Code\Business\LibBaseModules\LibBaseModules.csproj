<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A510D96B-71BB-42B8-B2DF-BB3BD8D89A72}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LibBaseModules</RootNamespace>
    <AssemblyName>LibBaseModules</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\Product\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\..\Product\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Communication\ComBus.cs" />
    <Compile Include="Communication\ComPipe.cs" />
    <Compile Include="DB\BaseNode.cs" />
    <Compile Include="DB\SqlSugarHelper.cs" />
    <Compile Include="Helper\AesCryptHelper.cs" />
    <Compile Include="Helper\ControlCallHelper.cs" />
    <Compile Include="Helper\Crc16.cs" />
    <Compile Include="Helper\CrcHelper.cs" />
    <Compile Include="Helper\DataConverterHelper.cs" />
    <Compile Include="Helper\DataGridViewHelper.cs" />
    <Compile Include="Helper\ExtensionsMethods.cs" />
    <Compile Include="Helper\FTPHelper.cs" />
    <Compile Include="Helper\FileExportHelper.cs" />
    <Compile Include="Helper\LogUtil.cs" />
    <Compile Include="Helper\SerializeHelper.cs" />
    <Compile Include="Helper\WinApiUtil\CommDataType\DCB.cs" />
    <Compile Include="Helper\WinApiUtil\CommDataType\PortSettings.cs" />
    <Compile Include="Helper\WinApiUtil\CommDataType\StructsAndEnums.cs" />
    <Compile Include="Helper\WinApiUtil\Enums.cs" />
    <Compile Include="Helper\WinApiUtil\WinApiWrapper.cs" />
    <Compile Include="Json\BaseJsonNode.cs" />
    <Compile Include="Json\JsonHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="UI\FrmLogInfoShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\FrmLogInfoShow.Designer.cs">
      <DependentUpon>FrmLogInfoShow.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\UC_LogInfoShow.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\UC_LogInfoShow.Designer.cs">
      <DependentUpon>UC_LogInfoShow.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FrmLogInfoShow.resx">
      <DependentUpon>FrmLogInfoShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\UC_LogInfoShow.resx">
      <DependentUpon>UC_LogInfoShow.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MySql.Data">
      <Version>9.3.0</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="NPOI">
      <Version>2.7.3</Version>
    </PackageReference>
    <PackageReference Include="SqlSugar">
      <Version>5.1.4.195</Version>
    </PackageReference>
    <PackageReference Include="SunnyUI">
      <Version>3.8.2</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>