﻿using LibBaseModules.Helper;
using LibBusinessModules.Helper;
using SqlSugar;
using Sunny.UI;
using System;
using System.Collections.Generic;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_DBInfoManager : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 原始数据库配置信息的深拷贝，用于对比变更
        /// </summary>
        private DBInfo _originalDBInfo = new DBInfo();

        #endregion

        #region 构造

        public UC_DBInfoManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_DBInfoManager_Load(object sender, EventArgs e)
        {
            // 保存原始配置的深拷贝
            SaveOriginalDBInfo();
            RefreshUI();
        }

        private void rdbSqlite_CheckedChanged(object sender, EventArgs e)
        {
            pnlMysqlConfig.Enabled = !rdbSqlite.Checked;
        }

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if(string.IsNullOrEmpty(txtDatabaseName.Text))
                {
                    throw new Exception("数据库名不可为空！");
                }

                if(rdbMySql.Checked)
                {
                    if(string.IsNullOrEmpty(txtServerIP.Text))
                    {
                        throw new Exception("数据库IP不可为空！");
                    }
                    if(txtServerPort.Value == 0)
                    {
                        throw new Exception("数据库端口不可为空！");
                    }
                    if(string.IsNullOrEmpty(txtUserName.Text))
                    {
                        throw new Exception("数据库用户名不可为空！");
                    }
                    if(string.IsNullOrEmpty(txtPassword.Text))
                    {
                        throw new Exception("数据库密码不可为空！");
                    }
                }

                // 更新配置
                SystemConfig.GetInstance().DBInfo.DatebaseType = rdbSqlite.Checked ? DbType.Sqlite : DbType.MySql;
                SystemConfig.GetInstance().DBInfo.DatabaseName = txtDatabaseName.Text;
                if(rdbMySql.Checked)
                {
                    SystemConfig.GetInstance().DBInfo.ServerIP = txtServerIP.Text;
                    SystemConfig.GetInstance().DBInfo.ServerPort = txtServerPort.Value;
                    SystemConfig.GetInstance().DBInfo.UserName = txtUserName.Text;
                    SystemConfig.GetInstance().DBInfo.Password = txtPassword.Text;
                }

                // 保存配置
                SystemConfig.GetInstance().Save();

                // 对比配置变更并记录日志
                LogConfigurationChanges();

                // 更新原始配置副本
                SaveOriginalDBInfo();

                UIMessageBox.ShowSuccess("保存成功！");
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"数据库配置保存失败：{ex.Message}", "配置修改");
                UIMessageBox.ShowError($"保存出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            SystemConfig.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            rdbSqlite.Checked = SystemConfig.GetInstance().DBInfo.DatebaseType == DbType.Sqlite;
            rdbMySql.Checked = SystemConfig.GetInstance().DBInfo.DatebaseType == DbType.MySql;

            txtDatabaseName.Text = SystemConfig.GetInstance().DBInfo.DatabaseName;
            txtServerIP.Text = SystemConfig.GetInstance().DBInfo.ServerIP;
            txtServerPort.Value = SystemConfig.GetInstance().DBInfo.ServerPort;
            txtUserName.Text = SystemConfig.GetInstance().DBInfo.UserName;
            txtPassword.Text = SystemConfig.GetInstance().DBInfo.Password;
        }

        #endregion

        #region 私有方法 - 日志记录

        /// <summary>
        /// 保存原始数据库配置信息的深拷贝
        /// </summary>
        private void SaveOriginalDBInfo()
        {
            try
            {
                var currentDBInfo = SystemConfig.GetInstance().DBInfo;
                _originalDBInfo = SerializeHelper.DeepCopyData(currentDBInfo);
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"保存原始数据库配置失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比配置变更并记录日志
        /// </summary>
        private void LogConfigurationChanges()
        {
            try
            {
                var currentDBInfo = SystemConfig.GetInstance().DBInfo;
                var changes = CompareDBConfigurations(_originalDBInfo, currentDBInfo);

                if(changes.Count > 0)
                {
                    LogHelper.LogInfo($"数据库配置修改：{string.Join("，", changes)}", "配置修改");
                }
                else
                {
                    LogHelper.LogInfo("数据库配置保存成功，无配置变更", "配置修改");
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"记录配置变更日志失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比两个数据库配置的差异
        /// </summary>
        /// <param name="originalInfo">原始配置</param>
        /// <param name="currentInfo">当前配置</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareDBConfigurations(DBInfo originalInfo, DBInfo currentInfo)
        {
            var changes = new List<string>();

            try
            {
                if(originalInfo.DatebaseType != currentInfo.DatebaseType)
                    changes.Add($"数据库类型[{originalInfo.DatebaseType} -> {currentInfo.DatebaseType}]");

                if(originalInfo.DatabaseName != currentInfo.DatabaseName)
                    changes.Add($"数据库名[{originalInfo.DatabaseName} -> {currentInfo.DatabaseName}]");

                if(originalInfo.ServerIP != currentInfo.ServerIP)
                    changes.Add($"服务器IP[{originalInfo.ServerIP} -> {currentInfo.ServerIP}]");

                if(originalInfo.ServerPort != currentInfo.ServerPort)
                    changes.Add($"服务器端口[{originalInfo.ServerPort} -> {currentInfo.ServerPort}]");

                if(originalInfo.UserName != currentInfo.UserName)
                    changes.Add($"用户名[{originalInfo.UserName} -> {currentInfo.UserName}]");

                // 密码字段特殊处理 - 不显示具体密码值
                if(originalInfo.Password != currentInfo.Password)
                    changes.Add($"密码已修改");
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比数据库配置失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        #endregion
    }
}