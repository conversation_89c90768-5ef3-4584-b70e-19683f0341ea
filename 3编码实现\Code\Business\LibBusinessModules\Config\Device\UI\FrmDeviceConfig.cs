﻿using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.Config.UI
{
    /// <summary>
    /// 单因子信息编辑
    /// </summary>
    public partial class FrmDeviceConfig : UIForm
    {
        #region 字段属性

        /// <summary>
        /// 界面对应因子配置信息
        /// </summary>
        public DeviceConfig DeviceConfigInfo;

        /// <summary>
        /// 当前处于编辑模式
        /// </summary>
        private bool _isEdit;

        /// <summary>
        /// 测试项编辑界面列表
        /// </summary>
        private List<UC_MeasureItemConfig> _ucMeasureItemList = new List<UC_MeasureItemConfig>();

        #endregion

        #region 构造

        public FrmDeviceConfig()
        {
            InitializeComponent();
        }

        public FrmDeviceConfig(DeviceConfig deviceConfigInfo, bool isEdit = false) : this()
        {
            DeviceConfigInfo = deviceConfigInfo;
            _isEdit = isEdit;
        }

        #endregion

        #region 事件

        private void FrmDeviceConfig_Load(object sender, EventArgs e)
        {
            // 编辑模式下，ID识别码不可修改
            txtIdCode.Enabled = !_isEdit;
            if(DeviceConfigInfo != null)
            {
                txtFactor.Text = DeviceConfigInfo.Factor;
                txtIdCode.Text = DeviceConfigInfo.IdCode;
                txtModel.Text = DeviceConfigInfo.Model;
                txtInspectionBasis.Text = DeviceConfigInfo.InspectionBasis;
                txtStandardRange.Text = DeviceConfigInfo.StandardRange.ToString("F2");
                txtStandardRangeUnit.Text = DeviceConfigInfo.StandardRangeUnit;
                txtAuxiliaryRange.Text = DeviceConfigInfo.AuxiliaryRange.ToString("F2");
                txtAuxiliaryRangeUnit.Text = DeviceConfigInfo.AuxiliaryRangeUnit;
                txtDataAccuracy.Text = DeviceConfigInfo.DataAccuracy.ToString();

                foreach(var item in DeviceConfigInfo.StandardRangeMeasureItems)
                {
                    var ucMeasureItemConfig = new UC_MeasureItemConfig(item);
                    pnlSRMeasureItems.Controls.Add(ucMeasureItemConfig);
                    _ucMeasureItemList.Add(ucMeasureItemConfig);
                }

                foreach(var item in DeviceConfigInfo.AuxiliaryRangeMeasureItems)
                {
                    var ucMeasureItemConfig = new UC_MeasureItemConfig(item);
                    pnlARMeasureItems.Controls.Add(ucMeasureItemConfig);
                    _ucMeasureItemList.Add(ucMeasureItemConfig);
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                DeviceConfigInfo.Factor = txtFactor.Text;
                DeviceConfigInfo.Model = txtModel.Text;
                DeviceConfigInfo.IdCode = txtIdCode.Text;
                DeviceConfigInfo.InspectionBasis = txtInspectionBasis.Text;
                DeviceConfigInfo.StandardRange = double.Parse(txtStandardRange.Text);
                DeviceConfigInfo.StandardRangeUnit = txtStandardRangeUnit.Text;
                DeviceConfigInfo.AuxiliaryRange = double.Parse(txtAuxiliaryRange.Text);
                DeviceConfigInfo.AuxiliaryRangeUnit = txtAuxiliaryRangeUnit.Text;
                DeviceConfigInfo.DataAccuracy = uint.Parse(txtDataAccuracy.Text);

                foreach(var uc in _ucMeasureItemList)
                {
                    uc.Save();
                }

                DialogResult = DialogResult.OK;
                this.Close();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"保存失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        private void Check()
        {
            if(string.IsNullOrWhiteSpace(txtFactor.Text))
            {
                throw new Exception("因子类型不能为空！");
            }
            if(string.IsNullOrWhiteSpace(txtModel.Text))
            {
                throw new Exception("设备型号不能为空！");
            }
            if(string.IsNullOrWhiteSpace(txtIdCode.Text))
            {
                throw new Exception("ID识别码不能为空！");
            }
            if(string.IsNullOrWhiteSpace(txtInspectionBasis.Text))
            {
                throw new Exception("检验依据不能为空！");
            }
            if(!double.TryParse(txtStandardRange.Text, out double standardRange))
            {
                throw new Exception("测试标准量程必须为数字！");
            }
            if(string.IsNullOrWhiteSpace(txtStandardRangeUnit.Text))
            {
                throw new Exception("测试标准量程单位不能为空！");
            }
            if(!double.TryParse(txtAuxiliaryRange.Text, out double auxiliaryRange))
            {
                throw new Exception("测试辅助量程必须为数字！");
            }
            if(string.IsNullOrWhiteSpace(txtAuxiliaryRangeUnit.Text))
            {
                throw new Exception("测试辅助量程单位不能为空！");
            }
            if(!uint.TryParse(txtDataAccuracy.Text, out uint dataAccuracy))
            {
                throw new Exception("数据精度必须为数字！");
            }

            // 新增模式下，校验ID识别码是否重复
            if(!_isEdit)
            {
                if(DeviceManager.GetInstance().GetDeviceList().Any(x => x.IdCode == txtIdCode.Text))
                {
                    throw new Exception("ID识别码重复！");
                }
            }

            foreach(var uc in _ucMeasureItemList)
            {
                uc.Check();
            }
        }

        #endregion
    }
}