﻿using LibBaseModules.Helper;
using LibBusinessModules.Helper;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_DeviceManager : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 原始设备配置列表的深拷贝，用于对比变更
        /// </summary>
        private List<DeviceConfig> _originalDeviceList = new List<DeviceConfig>();

        #endregion

        #region 构造

        public UC_DeviceManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_DeviceManager_Load(object sender, EventArgs e)
        {
            dgvDeviceList.Columns.Clear();

            dgvDeviceList.Columns.Add("Number", "序号");
            dgvDeviceList.Columns.Add("Factor", "因子类型");
            dgvDeviceList.Columns.Add("Model", "设备型号");
            dgvDeviceList.Columns.Add("IdCode", "ID识别码");
            dgvDeviceList.Columns.Add("InspectionBasis", "检验依据");
            dgvDeviceList.Columns.Add("StandardRange", "标准量程");
            dgvDeviceList.Columns.Add("StandardRangeUnit", "标准量程单位");
            dgvDeviceList.Columns.Add("AuxiliaryRange", "辅助量程");
            dgvDeviceList.Columns.Add("AuxiliaryRangeUnit", "辅助量程");

            foreach(DataGridViewColumn column in dgvDeviceList.Columns)
            {
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            // 保存原始配置的深拷贝
            SaveOriginalDeviceList();

            RefreshUI();
        }

        #region 按钮编辑

        private void btnAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 右键菜单编辑

        private void tsmAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void tsmEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void tsmDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 双击控件

        private void dgvDeviceList_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            EditDeviceConfig();
        }

        #endregion

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // 保存配置
                DeviceManager.GetInstance().Save();

                // 对比配置变更并记录日志
                LogConfigurationChanges();

                // 更新原始配置副本
                SaveOriginalDeviceList();

                UIMessageBox.ShowSuccess("保存成功！");
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"设备配置保存失败：{ex.Message}", "配置修改");
                UIMessageBox.ShowError($"保存失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            DeviceManager.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            dgvDeviceList.Rows.Clear();
            int number = 1;
            foreach(DeviceConfig deviceConfig in DeviceManager.GetInstance().GetDeviceList())
            {
                int rowIndex = dgvDeviceList.AddRow();
                DataGridViewRow dr = dgvDeviceList.Rows[rowIndex];
                dr.Cells["Number"].Value = number;
                dr.Cells["Factor"].Value = deviceConfig.Factor;
                dr.Cells["Model"].Value = deviceConfig.Model;
                dr.Cells["IdCode"].Value = deviceConfig.IdCode;
                dr.Cells["InspectionBasis"].Value = deviceConfig.InspectionBasis;
                dr.Cells["StandardRange"].Value = deviceConfig.StandardRange;
                dr.Cells["StandardRangeUnit"].Value = deviceConfig.StandardRangeUnit;
                dr.Cells["AuxiliaryRange"].Value = deviceConfig.AuxiliaryRange;
                dr.Cells["AuxiliaryRangeUnit"].Value = deviceConfig.AuxiliaryRangeUnit;

                dr.Tag = deviceConfig;

                number++;
            }
        }

        #region 增删改

        private void AddDeviceConfig()
        {
            try
            {
                DeviceConfig deviceConfig = new DeviceConfig();
                deviceConfig.InitMeasureItems();
                if(new FrmDeviceConfig(deviceConfig).ShowDialog() == DialogResult.OK)
                {
                    DeviceManager.GetInstance().DeviceList.Add(deviceConfig);
                    RefreshUI();
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"添加失败：{ex.Message}");
            }
        }

        private void EditDeviceConfig()
        {
            try
            {
                if(dgvDeviceList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待编辑因子！");
                }
                if(dgvDeviceList.SelectedRows[0].Tag is DeviceConfig deviceConfig)
                {
                    if(new FrmDeviceConfig(deviceConfig, true).ShowDialog() == DialogResult.OK)
                    {
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"编辑失败：{ex.Message}");
            }
        }

        private void DeleteDeviceConfig()
        {
            try
            {
                if(dgvDeviceList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待编辑因子！");
                }
                if(dgvDeviceList.SelectedRows[0].Tag is DeviceConfig deviceConfig)
                {
                    if(UIMessageBox.ShowAsk($"确认删除因子[{deviceConfig.Factor}]配置信息？"))
                    {
                        DeviceManager.GetInstance().DeviceList.Remove(deviceConfig);
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"删除失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 保存原始设备配置列表的深拷贝
        /// </summary>
        private void SaveOriginalDeviceList()
        {
            try
            {
                var currentDeviceList = DeviceManager.GetInstance().GetDeviceList();
                _originalDeviceList = SerializeHelper.DeepCopyData(currentDeviceList);
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"保存原始设备配置失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比配置变更并记录日志
        /// </summary>
        private void LogConfigurationChanges()
        {
            try
            {
                var currentDeviceList = DeviceManager.GetInstance().GetDeviceList();
                var changes = CompareDeviceConfigurations(_originalDeviceList, currentDeviceList);

                if(changes.Count > 0)
                {
                    foreach(var change in changes)
                    {
                        LogHelper.LogInfo(change, "配置修改");
                    }
                }
                else
                {
                    LogHelper.LogInfo("设备配置保存成功，无配置变更", "配置修改");
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"记录配置变更日志失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比两个设备配置列表的差异
        /// </summary>
        /// <param name="originalList">原始配置列表</param>
        /// <param name="currentList">当前配置列表</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareDeviceConfigurations(List<DeviceConfig> originalList, List<DeviceConfig> currentList)
        {
            var changes = new List<string>();

            try
            {
                // 检查新增的设备
                var addedDevices = currentList.Where(c => !originalList.Any(o => o.IdCode == c.IdCode)).ToList();
                foreach(var device in addedDevices)
                {
                    changes.Add($"新增设备配置：因子[{device.Factor}]，型号[{device.Model}]，编码[{device.IdCode}]");
                }

                // 检查删除的设备
                var deletedDevices = originalList.Where(o => !currentList.Any(c => c.IdCode == o.IdCode)).ToList();
                foreach(var device in deletedDevices)
                {
                    changes.Add($"删除设备配置：因子[{device.Factor}]，型号[{device.Model}]，编码[{device.IdCode}]");
                }

                // 检查修改的设备
                var modifiedDevices = currentList.Where(c => originalList.Any(o => o.IdCode == c.IdCode)).ToList();
                foreach(var currentDevice in modifiedDevices)
                {
                    var originalDevice = originalList.FirstOrDefault(o => o.IdCode == currentDevice.IdCode);
                    if(originalDevice != null)
                    {
                        var deviceChanges = CompareDeviceConfig(originalDevice, currentDevice);
                        if(deviceChanges.Count > 0)
                        {
                            changes.Add($"修改设备配置[{currentDevice.Factor}]：{string.Join("，", deviceChanges)}");
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比设备配置失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        /// <summary>
        /// 对比单个设备配置的差异
        /// </summary>
        /// <param name="original">原始配置</param>
        /// <param name="current">当前配置</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareDeviceConfig(DeviceConfig original, DeviceConfig current)
        {
            var changes = new List<string>();

            try
            {
                if(original.Factor != current.Factor)
                    changes.Add($"因子类型[{original.Factor} -> {current.Factor}]");

                if(original.Model != current.Model)
                    changes.Add($"设备型号[{original.Model} -> {current.Model}]");

                if(original.InspectionBasis != current.InspectionBasis)
                    changes.Add($"检验依据[{original.InspectionBasis} -> {current.InspectionBasis}]");

                if(Math.Abs(original.StandardRange - current.StandardRange) > 0.001)
                    changes.Add($"标准量程[{original.StandardRange} -> {current.StandardRange}]");

                if(original.StandardRangeUnit != current.StandardRangeUnit)
                    changes.Add($"标准量程单位[{original.StandardRangeUnit} -> {current.StandardRangeUnit}]");

                if(Math.Abs(original.AuxiliaryRange - current.AuxiliaryRange) > 0.001)
                    changes.Add($"辅助量程[{original.AuxiliaryRange} -> {current.AuxiliaryRange}]");

                if(original.AuxiliaryRangeUnit != current.AuxiliaryRangeUnit)
                    changes.Add($"辅助量程单位[{original.AuxiliaryRangeUnit} -> {current.AuxiliaryRangeUnit}]");

                if(original.DataAccuracy != current.DataAccuracy)
                    changes.Add($"数据精度[{original.DataAccuracy} -> {current.DataAccuracy}]");

                // 对比测试项配置
                var measureItemChanges = CompareMeasureItems(original, current);
                changes.AddRange(measureItemChanges);
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比单个设备配置失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        /// <summary>
        /// 对比测试项配置的差异
        /// </summary>
        /// <param name="original">原始配置</param>
        /// <param name="current">当前配置</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareMeasureItems(DeviceConfig original, DeviceConfig current)
        {
            var changes = new List<string>();

            try
            {
                // 对比标准量程测试项
                var standardChanges = CompareMeasureItemList(original.StandardRangeMeasureItems, current.StandardRangeMeasureItems, "标准量程");
                changes.AddRange(standardChanges);

                // 对比辅助量程测试项
                var auxiliaryChanges = CompareMeasureItemList(original.AuxiliaryRangeMeasureItems, current.AuxiliaryRangeMeasureItems, "辅助量程");
                changes.AddRange(auxiliaryChanges);
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比测试项配置失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        /// <summary>
        /// 对比测试项列表的差异
        /// </summary>
        /// <param name="originalItems">原始测试项列表</param>
        /// <param name="currentItems">当前测试项列表</param>
        /// <param name="rangeType">量程类型</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareMeasureItemList(List<MeasureItem> originalItems, List<MeasureItem> currentItems, string rangeType)
        {
            var changes = new List<string>();

            try
            {
                if(originalItems == null || currentItems == null) return changes;

                foreach(var currentItem in currentItems)
                {
                    var originalItem = originalItems.FirstOrDefault(o => o.ItemType == currentItem.ItemType);
                    if(originalItem != null)
                    {
                        var itemChanges = new List<string>();

                        if(originalItem.IsUsed != currentItem.IsUsed)
                            itemChanges.Add($"启用状态[{originalItem.IsUsed} -> {currentItem.IsUsed}]");

                        if(Math.Abs(originalItem.StandValue - currentItem.StandValue) > 0.00001)
                            itemChanges.Add($"浓度值[{originalItem.StandValue} -> {currentItem.StandValue}]");

                        if(Math.Abs(originalItem.QualifiedStandard - currentItem.QualifiedStandard) > 0.00001)
                            itemChanges.Add($"合格标准[{originalItem.QualifiedStandard} -> {currentItem.QualifiedStandard}]");

                        if(originalItem.ValueRange != currentItem.ValueRange)
                            itemChanges.Add($"取值范围[{originalItem.ValueRange} -> {currentItem.ValueRange}]");

                        if(itemChanges.Count > 0)
                        {
                            changes.Add($"{rangeType}测试项[{currentItem.Name}]：{string.Join("，", itemChanges)}");
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比测试项列表失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        #endregion

        #endregion
    }
}