﻿using LibBaseModules.Helper;
using LibBusinessModules.Helper;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_EmployeeInfoManager : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 原始员工信息列表的深拷贝，用于对比变更
        /// </summary>
        private List<EmployeeInfo> _originalEmployeeList = new List<EmployeeInfo>();

        #endregion

        #region 构造

        public UC_EmployeeInfoManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_EmployeeInfoManager_Load(object sender, EventArgs e)
        {
            dgvEmployeeList.Columns.Clear();
            dgvEmployeeList.Columns.Add("Number", "序号");
            dgvEmployeeList.Columns.Add("ID", "工号");
            dgvEmployeeList.Columns.Add("Name", "姓名");
            dgvEmployeeList.Columns.Add("Position", "岗位");

            foreach(DataGridViewColumn column in dgvEmployeeList.Columns)
            {
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            // 保存原始配置的深拷贝
            SaveOriginalEmployeeList();
            RefreshUI();
        }

        #region 按钮编辑

        private void btnAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 右键菜单编辑

        private void tsmAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void tsmEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void tsmDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 双击控件

        private void dgvEmployeeList_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            EditDeviceConfig();
        }

        #endregion

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // 保存配置
                SystemConfig.GetInstance().Save();

                // 对比配置变更并记录日志
                LogConfigurationChanges();

                // 更新原始配置副本
                SaveOriginalEmployeeList();

                UIMessageBox.ShowSuccess("保存成功！");
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"人员信息配置保存失败：{ex.Message}", "配置修改");
                UIMessageBox.ShowError($"保存失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            SystemConfig.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            dgvEmployeeList.Rows.Clear();
            int number = 1;
            foreach(EmployeeInfo employeeInfo in SystemConfig.GetInstance().GetAllEmployeeInfo())
            {
                int rowIndex = dgvEmployeeList.AddRow();
                DataGridViewRow dr = dgvEmployeeList.Rows[rowIndex];
                dr.Cells["Number"].Value = number;
                dr.Cells["ID"].Value = employeeInfo.ID;
                dr.Cells["Name"].Value = employeeInfo.Name;
                dr.Cells["Position"].Value = employeeInfo.Position;

                dr.Tag = employeeInfo;

                number++;
            }
        }

        #region 增删改

        private void AddDeviceConfig()
        {
            try
            {
                EmployeeInfo employeeInfo = new EmployeeInfo();
                if(new FrmEmployeeInfoConfig(employeeInfo).ShowDialog() == DialogResult.OK)
                {
                    SystemConfig.GetInstance().EmployeeList.Add(employeeInfo);
                    RefreshUI();
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"添加员工信息失败：{ex.Message}", "配置修改");
                UIMessageBox.ShowError($"添加失败：{ex.Message}");
            }
        }

        private void EditDeviceConfig()
        {
            try
            {
                if(dgvEmployeeList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待编辑员工！");
                }
                if(dgvEmployeeList.SelectedRows[0].Tag is EmployeeInfo employeeInfo)
                {
                    if(new FrmEmployeeInfoConfig(employeeInfo, true).ShowDialog() == DialogResult.OK)
                    {
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"编辑员工信息失败：{ex.Message}", "配置修改");
                UIMessageBox.ShowError($"编辑失败：{ex.Message}");
            }
        }

        private void DeleteDeviceConfig()
        {
            try
            {
                if(dgvEmployeeList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待删除员工！");
                }
                if(dgvEmployeeList.SelectedRows[0].Tag is EmployeeInfo employeeInfo)
                {
                    if(UIMessageBox.ShowAsk($"确认删除员工[{employeeInfo.Name}]信息？"))
                    {
                        SystemConfig.GetInstance().EmployeeList.Remove(employeeInfo);
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"删除员工信息失败：{ex.Message}", "配置修改");
                UIMessageBox.ShowError($"删除失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法 - 日志记录

        /// <summary>
        /// 保存原始员工信息列表的深拷贝
        /// </summary>
        private void SaveOriginalEmployeeList()
        {
            try
            {
                var currentEmployeeList = SystemConfig.GetInstance().GetAllEmployeeInfo();
                _originalEmployeeList = SerializeHelper.DeepCopyData(currentEmployeeList);
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"保存原始员工信息失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比配置变更并记录日志
        /// </summary>
        private void LogConfigurationChanges()
        {
            try
            {
                var currentEmployeeList = SystemConfig.GetInstance().GetAllEmployeeInfo();
                var changes = CompareEmployeeConfigurations(_originalEmployeeList, currentEmployeeList);

                if(changes.Count > 0)
                {
                    foreach(var change in changes)
                    {
                        LogHelper.LogInfo(change, "配置修改");
                    }
                }
                else
                {
                    LogHelper.LogInfo("人员信息配置保存成功，无配置变更", "配置修改");
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"记录配置变更日志失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比两个员工信息列表的差异
        /// </summary>
        /// <param name="originalList">原始配置列表</param>
        /// <param name="currentList">当前配置列表</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareEmployeeConfigurations(List<EmployeeInfo> originalList, List<EmployeeInfo> currentList)
        {
            var changes = new List<string>();

            try
            {
                // 检查新增的员工
                var addedEmployees = currentList.Where(c => !originalList.Any(o => o.ID == c.ID)).ToList();
                foreach(var employee in addedEmployees)
                {
                    changes.Add($"新增员工信息：工号[{employee.ID}]，姓名[{employee.Name}]，职位[{employee.Position}]");
                }

                // 检查删除的员工
                var deletedEmployees = originalList.Where(o => !currentList.Any(c => c.ID == o.ID)).ToList();
                foreach(var employee in deletedEmployees)
                {
                    changes.Add($"删除员工信息：工号[{employee.ID}]，姓名[{employee.Name}]，职位[{employee.Position}]");
                }

                // 检查修改的员工
                var modifiedEmployees = currentList.Where(c => originalList.Any(o => o.ID == c.ID)).ToList();
                foreach(var currentEmployee in modifiedEmployees)
                {
                    var originalEmployee = originalList.FirstOrDefault(o => o.ID == currentEmployee.ID);
                    if(originalEmployee != null)
                    {
                        var employeeChanges = CompareEmployeeInfo(originalEmployee, currentEmployee);
                        if(employeeChanges.Count > 0)
                        {
                            changes.Add($"修改员工信息[{currentEmployee.Name}]：{string.Join("，", employeeChanges)}");
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比员工信息失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        /// <summary>
        /// 对比单个员工信息的差异
        /// </summary>
        /// <param name="original">原始信息</param>
        /// <param name="current">当前信息</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareEmployeeInfo(EmployeeInfo original, EmployeeInfo current)
        {
            var changes = new List<string>();

            try
            {
                if(original.Name != current.Name)
                    changes.Add($"姓名[{original.Name} -> {current.Name}]");

                if(original.Position != current.Position)
                    changes.Add($"职位[{original.Position} -> {current.Position}]");

                if(original.Phone != current.Phone)
                    changes.Add($"联系方式[{original.Phone} -> {current.Phone}]");
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比单个员工信息失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        #endregion

        #endregion
    }
}