﻿using LibBaseModules.Helper;
using LibBusinessModules.Helper;
using Sunny.UI;
using System;
using System.Collections.Generic;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_FTPInfoManager : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 原始FTP配置信息的深拷贝，用于对比变更
        /// </summary>
        private FTPInfo _originalFTPInfo = new FTPInfo();

        #endregion

        #region 构造

        public UC_FTPInfoManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_DBInfoManager_Load(object sender, EventArgs e)
        {
            // 保存原始配置的深拷贝
            SaveOriginalFTPInfo();
            RefreshUI();
        }

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if(string.IsNullOrEmpty(txtServerIP.Text))
                {
                    throw new Exception("FTP服务IP不可为空！");
                }
                if(txtServerPort.Value == 0)
                {
                    throw new Exception("FTP服务端口不可为空！");
                }
                if(string.IsNullOrEmpty(txtUserName.Text))
                {
                    throw new Exception("用户名不可为空！");
                }
                if(string.IsNullOrEmpty(txtPassword.Text))
                {
                    throw new Exception("密码不可为空！");
                }
                if(string.IsNullOrEmpty(txtFilePath.Text))
                {
                    throw new Exception("数据库文件路径不可为空！");
                }

                // 更新配置
                SystemConfig.GetInstance().FTPInfo.ServerIP = txtServerIP.Text;
                SystemConfig.GetInstance().FTPInfo.ServerPort = txtServerPort.Value;
                SystemConfig.GetInstance().FTPInfo.UserName = txtUserName.Text;
                SystemConfig.GetInstance().FTPInfo.Password = txtPassword.Text;
                SystemConfig.GetInstance().FTPInfo.FilePath = txtFilePath.Text;

                // 保存配置
                SystemConfig.GetInstance().Save();

                // 对比配置变更并记录日志
                LogConfigurationChanges();

                // 更新原始配置副本
                SaveOriginalFTPInfo();

                UIMessageBox.ShowSuccess("保存成功！");
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"FTP配置保存失败：{ex.Message}", "配置修改");
                UIMessageBox.ShowError($"保存出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            SystemConfig.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            txtServerIP.Text = SystemConfig.GetInstance().FTPInfo.ServerIP;
            txtServerPort.Value = SystemConfig.GetInstance().FTPInfo.ServerPort;
            txtUserName.Text = SystemConfig.GetInstance().FTPInfo.UserName;
            txtPassword.Text = SystemConfig.GetInstance().FTPInfo.Password;
            txtFilePath.Text = SystemConfig.GetInstance().FTPInfo.FilePath;
        }

        #endregion

        #region 私有方法 - 日志记录

        /// <summary>
        /// 保存原始FTP配置信息的深拷贝
        /// </summary>
        private void SaveOriginalFTPInfo()
        {
            try
            {
                var currentFTPInfo = SystemConfig.GetInstance().FTPInfo;
                _originalFTPInfo = SerializeHelper.DeepCopyData(currentFTPInfo);
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"保存原始FTP配置失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比配置变更并记录日志
        /// </summary>
        private void LogConfigurationChanges()
        {
            try
            {
                var currentFTPInfo = SystemConfig.GetInstance().FTPInfo;
                var changes = CompareFTPConfigurations(_originalFTPInfo, currentFTPInfo);

                if(changes.Count > 0)
                {
                    foreach(var change in changes)
                    {
                        LogHelper.LogInfo($"FTP配置修改：{string.Join("，", changes)}", "配置修改");
                    }
                }
                else
                {
                    LogHelper.LogInfo("FTP配置保存成功，无配置变更", "配置修改");
                }
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"记录配置变更日志失败：{ex.Message}", "配置修改");
            }
        }

        /// <summary>
        /// 对比两个FTP配置的差异
        /// </summary>
        /// <param name="originalInfo">原始配置</param>
        /// <param name="currentInfo">当前配置</param>
        /// <returns>变更描述列表</returns>
        private List<string> CompareFTPConfigurations(FTPInfo originalInfo, FTPInfo currentInfo)
        {
            var changes = new List<string>();

            try
            {
                if(originalInfo.ServerIP != currentInfo.ServerIP)
                    changes.Add($"服务器IP[{originalInfo.ServerIP} -> {currentInfo.ServerIP}]");

                if(originalInfo.ServerPort != currentInfo.ServerPort)
                    changes.Add($"服务器端口[{originalInfo.ServerPort} -> {currentInfo.ServerPort}]");

                if(originalInfo.UserName != currentInfo.UserName)
                    changes.Add($"用户名[{originalInfo.UserName} -> {currentInfo.UserName}]");

                if(originalInfo.FilePath != currentInfo.FilePath)
                    changes.Add($"文件路径[{originalInfo.FilePath} -> {currentInfo.FilePath}]");

                // 密码字段特殊处理 - 不显示具体密码值
                if(originalInfo.Password != currentInfo.Password)
                    changes.Add($"密码已修改");
            }
            catch(Exception ex)
            {
                LogHelper.LogError($"对比FTP配置失败：{ex.Message}", "配置修改");
            }

            return changes;
        }

        #endregion
    }
}