using LibBusinessModules.Fault.Helper;
using Sunny.UI;
using System;

namespace LibBusinessModules.Fault
{
    /// <summary>
    /// 故障数据生成器窗体
    /// </summary>
    public partial class FrmFaultDataGenerator : UIForm
    {
        #region 字段属性

        private FaultDataGenerator _dataGenerator = new FaultDataGenerator();

        #endregion

        #region 构造函数

        public FrmFaultDataGenerator()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void FrmFaultDataGenerator_Load(object sender, EventArgs e)
        {
            // 设置默认值
            numRecordCount.Value = 150;
            numDeviceCount.Value = 25;
            numDayRange.Value = 30;
        }

        /// <summary>
        /// 生成数据按钮点击事件
        /// </summary>
        private void btnGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                int recordCount = (int)numRecordCount.Value;
                int deviceCount = (int)numDeviceCount.Value;
                int dayRange = (int)numDayRange.Value;

                _dataGenerator.GenerateTestData(recordCount, deviceCount, dayRange);
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"生成数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空数据按钮点击事件
        /// </summary>
        private void btnClear_Click(object sender, EventArgs e)
        {
            try
            {
                _dataGenerator.ClearAllTestData();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"清空数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空故障记录按钮点击事件
        /// </summary>
        private void btnClearFaultRecords_Click(object sender, EventArgs e)
        {
            try
            {
                _dataGenerator.ClearFaultRecordsOnly();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"清空故障记录失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空设备信息按钮点击事件
        /// </summary>
        private void btnClearDevices_Click(object sender, EventArgs e)
        {
            try
            {
                _dataGenerator.ClearGeneratedDevicesOnly();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"清空设备信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 快速生成按钮点击事件
        /// </summary>
        private void btnQuickGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                FaultDataGenerator.QuickGenerate();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"快速生成失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}
