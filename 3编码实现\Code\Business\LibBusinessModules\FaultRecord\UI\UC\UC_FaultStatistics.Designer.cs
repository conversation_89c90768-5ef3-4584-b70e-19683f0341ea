﻿namespace LibBusinessModules.Fault
{
    partial class UC_FaultStatistics
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.pnlTitle = new Sunny.UI.UIPanel();
            this.btnExcelPNG = new Sunny.UI.UIButton();
            this.btnGenerateData = new Sunny.UI.UIButton();
            this.dtpEndTime = new Sunny.UI.UIDatetimePicker();
            this.dtpStartTime = new Sunny.UI.UIDatetimePicker();
            this.btnStartQuery = new Sunny.UI.UIButton();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.category1Chart = new LiveChartsCore.SkiaSharpView.WinForms.CartesianChart();
            this.category2Chart = new LiveChartsCore.SkiaSharpView.WinForms.CartesianChart();
            this.factorChart = new LiveChartsCore.SkiaSharpView.WinForms.CartesianChart();
            this.totalChart = new LiveChartsCore.SkiaSharpView.WinForms.PieChart();
            this.pnlCharts = new Sunny.UI.UITableLayoutPanel();
            this.pnlTitle.SuspendLayout();
            this.pnlCharts.SuspendLayout();
            this.SuspendLayout();
            // 
            // saveFileDialog
            // 
            this.saveFileDialog.DefaultExt = "png";
            this.saveFileDialog.Filter = "图片文件|*.png";
            this.saveFileDialog.Title = "导出故障记录";
            // 
            // pnlTitle
            // 
            this.pnlTitle.Controls.Add(this.btnExcelPNG);
            this.pnlTitle.Controls.Add(this.btnGenerateData);
            this.pnlTitle.Controls.Add(this.dtpEndTime);
            this.pnlTitle.Controls.Add(this.dtpStartTime);
            this.pnlTitle.Controls.Add(this.btnStartQuery);
            this.pnlTitle.Controls.Add(this.uiLabel2);
            this.pnlTitle.Controls.Add(this.uiLabel3);
            this.pnlTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTitle.FillColor = System.Drawing.Color.White;
            this.pnlTitle.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.pnlTitle.Location = new System.Drawing.Point(1, 1);
            this.pnlTitle.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTitle.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTitle.Name = "pnlTitle";
            this.pnlTitle.Size = new System.Drawing.Size(1198, 50);
            this.pnlTitle.Style = Sunny.UI.UIStyle.Custom;
            this.pnlTitle.TabIndex = 8;
            this.pnlTitle.Text = null;
            this.pnlTitle.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnExcelPNG
            // 
            this.btnExcelPNG.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExcelPNG.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnExcelPNG.Font = new System.Drawing.Font("宋体", 12F);
            this.btnExcelPNG.Location = new System.Drawing.Point(1099, 9);
            this.btnExcelPNG.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnExcelPNG.Name = "btnExcelPNG";
            this.btnExcelPNG.Size = new System.Drawing.Size(92, 32);
            this.btnExcelPNG.Style = Sunny.UI.UIStyle.Custom;
            this.btnExcelPNG.TabIndex = 25;
            this.btnExcelPNG.Text = "PNG导出";
            this.btnExcelPNG.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExcelPNG.Click += new System.EventHandler(this.btnExcelPNG_Click);
            // 
            // btnGenerateData
            // 
            this.btnGenerateData.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGenerateData.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnGenerateData.Font = new System.Drawing.Font("宋体", 12F);
            this.btnGenerateData.Location = new System.Drawing.Point(893, 9);
            this.btnGenerateData.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnGenerateData.Name = "btnGenerateData";
            this.btnGenerateData.Size = new System.Drawing.Size(92, 32);
            this.btnGenerateData.Style = Sunny.UI.UIStyle.Custom;
            this.btnGenerateData.TabIndex = 26;
            this.btnGenerateData.Text = "生成数据";
            this.btnGenerateData.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnGenerateData.Click += new System.EventHandler(this.btnGenerateData_Click);
            // 
            // dtpEndTime
            // 
            this.dtpEndTime.FillColor = System.Drawing.Color.White;
            this.dtpEndTime.Font = new System.Drawing.Font("宋体", 12F);
            this.dtpEndTime.Location = new System.Drawing.Point(355, 11);
            this.dtpEndTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpEndTime.MaxLength = 19;
            this.dtpEndTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpEndTime.Name = "dtpEndTime";
            this.dtpEndTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpEndTime.Size = new System.Drawing.Size(186, 29);
            this.dtpEndTime.Style = Sunny.UI.UIStyle.Custom;
            this.dtpEndTime.SymbolDropDown = 61555;
            this.dtpEndTime.SymbolNormal = 61555;
            this.dtpEndTime.SymbolSize = 24;
            this.dtpEndTime.TabIndex = 23;
            this.dtpEndTime.Text = "2021-01-25 10:00:03";
            this.dtpEndTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpEndTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpEndTime.Watermark = "";
            // 
            // dtpStartTime
            // 
            this.dtpStartTime.FillColor = System.Drawing.Color.White;
            this.dtpStartTime.Font = new System.Drawing.Font("宋体", 12F);
            this.dtpStartTime.Location = new System.Drawing.Point(79, 11);
            this.dtpStartTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpStartTime.MaxLength = 19;
            this.dtpStartTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpStartTime.Size = new System.Drawing.Size(186, 29);
            this.dtpStartTime.Style = Sunny.UI.UIStyle.Custom;
            this.dtpStartTime.SymbolDropDown = 61555;
            this.dtpStartTime.SymbolNormal = 61555;
            this.dtpStartTime.SymbolSize = 24;
            this.dtpStartTime.TabIndex = 22;
            this.dtpStartTime.Text = "2021-01-25 10:00:03";
            this.dtpStartTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpStartTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpStartTime.Watermark = "";
            // 
            // btnStartQuery
            // 
            this.btnStartQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnStartQuery.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStartQuery.Font = new System.Drawing.Font("宋体", 12F);
            this.btnStartQuery.Location = new System.Drawing.Point(996, 9);
            this.btnStartQuery.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnStartQuery.Name = "btnStartQuery";
            this.btnStartQuery.Size = new System.Drawing.Size(92, 32);
            this.btnStartQuery.Style = Sunny.UI.UIStyle.Custom;
            this.btnStartQuery.TabIndex = 24;
            this.btnStartQuery.Text = "查询";
            this.btnStartQuery.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStartQuery.Click += new System.EventHandler(this.btnStartQuery_Click);
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.uiLabel2.Location = new System.Drawing.Point(284, 17);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(71, 16);
            this.uiLabel2.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel2.TabIndex = 33;
            this.uiLabel2.Text = "结束时间";
            this.uiLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.uiLabel3.Location = new System.Drawing.Point(7, 17);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(71, 16);
            this.uiLabel3.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel3.TabIndex = 32;
            this.uiLabel3.Text = "起始时间";
            this.uiLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // category1Chart
            // 
            this.category1Chart.BackColor = System.Drawing.Color.White;
            this.category1Chart.Dock = System.Windows.Forms.DockStyle.Fill;
            this.category1Chart.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.category1Chart.Location = new System.Drawing.Point(4, 5);
            this.category1Chart.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.category1Chart.MatchAxesScreenDataRatio = false;
            this.category1Chart.Name = "category1Chart";
            this.category1Chart.Size = new System.Drawing.Size(591, 249);
            this.category1Chart.TabIndex = 0;
            // 
            // category2Chart
            // 
            this.category2Chart.BackColor = System.Drawing.Color.White;
            this.category2Chart.Dock = System.Windows.Forms.DockStyle.Fill;
            this.category2Chart.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.category2Chart.Location = new System.Drawing.Point(603, 5);
            this.category2Chart.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.category2Chart.MatchAxesScreenDataRatio = false;
            this.category2Chart.Name = "category2Chart";
            this.category2Chart.Size = new System.Drawing.Size(591, 249);
            this.category2Chart.TabIndex = 1;
            // 
            // factorChart
            // 
            this.factorChart.BackColor = System.Drawing.Color.White;
            this.factorChart.Dock = System.Windows.Forms.DockStyle.Fill;
            this.factorChart.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.factorChart.Location = new System.Drawing.Point(4, 264);
            this.factorChart.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.factorChart.MatchAxesScreenDataRatio = false;
            this.factorChart.Name = "factorChart";
            this.factorChart.Size = new System.Drawing.Size(591, 249);
            this.factorChart.TabIndex = 2;
            // 
            // totalChart
            // 
            this.totalChart.BackColor = System.Drawing.Color.White;
            this.totalChart.Dock = System.Windows.Forms.DockStyle.Fill;
            this.totalChart.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.totalChart.InitialRotation = 0D;
            this.totalChart.IsClockwise = true;
            this.totalChart.Location = new System.Drawing.Point(603, 264);
            this.totalChart.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.totalChart.MaxAngle = 360D;
            this.totalChart.MaxValue = double.NaN;
            this.totalChart.MinValue = 0D;
            this.totalChart.Name = "totalChart";
            this.totalChart.Size = new System.Drawing.Size(591, 249);
            this.totalChart.TabIndex = 3;
            // 
            // pnlCharts
            // 
            this.pnlCharts.BackColor = System.Drawing.Color.White;
            this.pnlCharts.ColumnCount = 2;
            this.pnlCharts.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.pnlCharts.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.pnlCharts.Controls.Add(this.category1Chart, 0, 0);
            this.pnlCharts.Controls.Add(this.totalChart, 1, 1);
            this.pnlCharts.Controls.Add(this.factorChart, 0, 1);
            this.pnlCharts.Controls.Add(this.category2Chart, 1, 0);
            this.pnlCharts.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlCharts.Location = new System.Drawing.Point(1, 51);
            this.pnlCharts.Name = "pnlCharts";
            this.pnlCharts.RowCount = 2;
            this.pnlCharts.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.pnlCharts.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.pnlCharts.Size = new System.Drawing.Size(1198, 518);
            this.pnlCharts.TabIndex = 4;
            this.pnlCharts.TagString = null;
            // 
            // UC_FaultStatistics
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.pnlCharts);
            this.Controls.Add(this.pnlTitle);
            this.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            this.Name = "UC_FaultStatistics";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1200, 570);
            this.Text = "故障管理";
            this.Load += new System.EventHandler(this.UC_FaultRecordQuery_Load);
            this.pnlTitle.ResumeLayout(false);
            this.pnlTitle.PerformLayout();
            this.pnlCharts.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
        protected Sunny.UI.UIPanel pnlTitle;
        private Sunny.UI.UIButton btnExcelPNG;
        private Sunny.UI.UIButton btnGenerateData;
        private Sunny.UI.UIDatetimePicker dtpEndTime;
        private Sunny.UI.UIDatetimePicker dtpStartTime;
        private Sunny.UI.UIButton btnStartQuery;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel3;

        private Sunny.UI.UITableLayoutPanel pnlCharts;
        private LiveChartsCore.SkiaSharpView.WinForms.CartesianChart category1Chart;
        private LiveChartsCore.SkiaSharpView.WinForms.CartesianChart category2Chart;
        private LiveChartsCore.SkiaSharpView.WinForms.CartesianChart factorChart;
        private LiveChartsCore.SkiaSharpView.WinForms.PieChart totalChart;
    }
}
