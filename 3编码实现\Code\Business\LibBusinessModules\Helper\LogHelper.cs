using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using System;
using System.Threading.Tasks;

namespace LibBusinessModules.Helper
{
    /// <summary>
    /// 系统日志记录帮助器
    /// 提供统一的日志记录接口，支持异步写入数据库
    /// </summary>
    public static class LogHelper
    {
        #region 公共方法

        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogInfo(string content, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Info, operationType);
        }

        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogWarning(string content, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Warning, operationType);
        }

        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogError(string content, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Error, operationType);
        }

        /// <summary>
        /// 记录调试级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogDebug(string content, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Debug, operationType);
        }

        /// <summary>
        /// 记录致命错误级别日志
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="operationType">操作类型（可选）</param>
        public static void LogFatal(string content, string operationType = "")
        {
            WriteLogAsync(content, LogLevel.Fatal, operationType);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 异步写入日志到数据库
        /// </summary>
        /// <param name="content">日志内容</param>
        /// <param name="level">日志级别</param>
        /// <param name="operationType">操作类型</param>
        private static void WriteLogAsync(string content, LogLevel level, string operationType)
        {
            Task.Run(async () =>
            {
                try
                {
                    // 创建日志对象
                    var logData = new SystemLogData(content, level, operationType);

                    // 写入数据库
                    using(SqlSugar.SqlSugarClient db = DBHelper.GetPCDBContext())
                    {
                        await db.Insertable(logData).ExecuteCommandAsync();
                    }
                }
                catch(Exception ex)
                {
                    // 如果数据库写入失败，记录到文件日志
                    LogUtil.GetInstance().LogWrite($"写入系统日志失败: {ex.Message}", LibBaseModules.Helper.MsgLevel.Error);
                }
            });
        }

        #endregion
    }
}