﻿using LibBusinessModules.Config;
using LibBusinessModules.DB.Models.PC;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace LibBusinessModules.Report.Config
{
    /// <summary>
    /// 报表计算用测量数据，不区分设备类型
    /// </summary>
    public class ReportMeasureData
    {
        #region 字段属性

        /// <summary>
        /// 对应测量项信息
        /// </summary>
        public MeasureItem MeasureItem { get; set; }

        /// <summary>
        /// 对应量程
        ///</summary>
        [Description("对应量程")]
        public double Range { get; set; }

        /// <summary>
        /// 关联的测量原始数据
        /// </summary>
        public List<ReportTmpMeasureData> MeasureDataList { get; set; } = new List<ReportTmpMeasureData>();

        /// <summary>
        /// 计算值，非绝对值时单位百分比
        ///</summary>
        [Description("计算值")]
        public double ResultValue { get; set; }

        /// <summary>
        /// 是否合格
        ///</summary>
        [Description("是否合格")]
        public bool IsQualified => Math.Abs(ResultValue) < MeasureItem.QualifiedStandard;

        #endregion

        #region 公共方法

        public void CalculatedData(DeviceInfo devInfo)
        {
            // 示值误差1、示值误差2、葡萄糖试验
            if(MeasureItem.ItemType == eMeasureItemType.DemonstrationValueError1 || MeasureItem.ItemType == eMeasureItemType.DemonstrationValueError2 ||
                MeasureItem.ItemType == eMeasureItemType.GlucoseTest)
            {
                // 计算6次测量值的平均值
                double average = MeasureDataList.Average(data => data.MeasureValue);
                // 计算示值误差
                ResultValue = (average - MeasureItem.StandValue) / MeasureItem.StandValue * 100;
            }
            // 直线性
            else if(MeasureItem.ItemType == eMeasureItemType.Linearity)
            {
                // 计算6次测量值的平均值
                double average = MeasureDataList.Average(data => data.MeasureValue);
                // 计算直线性
                ResultValue = (average - MeasureItem.StandValue) / Range * 100;
            }
            // 重复性
            else if(MeasureItem.ItemType == eMeasureItemType.Repeatability)
            {
                // 计算平均值
                double average = MeasureDataList.Average(data => data.MeasureValue);

                // 计算平方和
                double sumOfSquares = MeasureDataList.Sum(data => Math.Pow(data.MeasureValue - average, 2));

                // 计算标准偏差
                double standardDeviation = Math.Sqrt(sumOfSquares / (MeasureDataList.Count - 1));

                // 计算相对标准偏差（重复性）
                ResultValue = standardDeviation / average * 100;
            }
            // 24小时低浓度漂移
            else if(MeasureItem.ItemType == eMeasureItemType.LowConcentrationDrift24h)
            {
                if(devInfo.IsCODDevice)
                {
                    // 计算最初3次测量值的平均值（Z₀）
                    double initialAverage = MeasureDataList.Take(3).Average(data => data.MeasureValue);

                    // 计算所有测量值与Z₀的偏差绝对值的最大值
                    double maxDeviation = MeasureDataList.Max(data => Math.Abs(data.MeasureValue - initialAverage));

                    // 计算24小时低浓度漂移
                    ResultValue = maxDeviation;
                }
                else if(devInfo.IsNH4Device)
                {
                    // 计算最初3次测量值的平均值（Z₀）
                    double initialAverage = MeasureDataList.Take(3).Average(data => data.MeasureValue);

                    // 计算所有测量值与Z₀的偏差绝对值的平均值
                    double averageDeviation = MeasureDataList.Average(data => Math.Abs(data.MeasureValue - initialAverage));

                    // 计算24小时高浓度漂移
                    ResultValue = averageDeviation * 100;
                }
                else
                {
                    throw new Exception("暂不支持当前类型设备计算24小时低浓度漂移数据！");
                }
            }
            // 24小时高浓度漂移
            else if(MeasureItem.ItemType == eMeasureItemType.HighConcentrationDrift24h)
            {
                // 计算最初3次测量值的平均值（R₀）
                double initialAverage = MeasureDataList.Take(3).Average(data => data.MeasureValue);

                // 计算所有测量值与R₀的偏差绝对值的平均值
                double averageDeviation = MeasureDataList.Average(data => Math.Abs(data.MeasureValue - initialAverage));

                // 计算24小时高浓度漂移
                ResultValue = averageDeviation / Range * 100;
            }
            // 零点漂移
            else if(MeasureItem.ItemType == eMeasureItemType.ZeroPointDrift)
            {
                // 计算最初3次测量值的平均值（Z₀）
                double initialAverage = MeasureDataList.Take(3).Average(data => data.MeasureValue);

                // 计算所有测量值与Z₀的差值，并找出最大差值
                double maxDifference = MeasureDataList.Max(data => Math.Abs(data.MeasureValue - initialAverage));

                // 计算零点漂移
                ResultValue = maxDifference / Range * 100;
            }
            // 量程漂移
            else if(MeasureItem.ItemType == eMeasureItemType.MeasurementRangeDrift)
            {
                // 计算零点漂移前量程校准液3次测量平均值（D₁）
                double d1 = MeasureDataList.Take(3).Average(data => data.MeasureValue);

                // 计算零点漂移后量程校准液3次测量平均值（D₂）
                double d2 = MeasureDataList.Skip(3).Take(3).Average(data => data.MeasureValue);

                // 计算量程漂移（实际使用时还需再减去零点漂移结果）
                ResultValue = (d2 - d1) / Range * 100;
            }
        }

        #endregion
    }

    /// <summary>
    /// 报表计算临时用测量数据，不区分设备类型
    /// </summary>
    public class ReportTmpMeasureData
    {
        /// <summary>
        /// 数据时间
        ///</summary>
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 标准值
        ///</summary>
        [Description("标准值")]
        public double StandValue { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [Description("测量值")]
        public double MeasureValue { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [Description("量程")]
        public double Range { get; set; }
    }
}
