﻿using LibBusinessModules.Report.Config;
using NPOI.XWPF.UserModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LibBusinessModules.Report.Helper
{
    /// <summary>
    /// 数据记录单导出帮助类
    /// </summary>
    internal static class DataRecordExportHelper
    {
        /// <summary>
        /// 导出数据记录单文档
        /// </summary>
        /// <param name="calculateNode">报表计算节点</param>
        public static string ExportWordFile(ReportCalculateNode calculateNode)
        {
            var outputPath = ReportPathHelper.GetReportSavePath(calculateNode, "数据记录单");
            var templateFilePath = ReportPathHelper.GetTemplateFilePath(calculateNode, "数据记录单");

            // 加载模板文件
            using var doc = ReportDocumentHelper.LoadDocument(templateFilePath);

            // 生成替换信息
            var replacements = PrepareReplacements(calculateNode);

            // 执行基础信息替换
            ReportDocumentHelper.ReplaceAllText(doc, replacements);

            // 插入光源信息记录
            InsertLightSourceInfo(doc, calculateNode);

            // 插入校准信息记录
            InsertCalibrationData(doc, calculateNode);

            // 插入测量结果记录
            InsertMeasureData(doc, calculateNode);

            // 保存结果文件
            ReportDocumentHelper.SaveDocument(doc, outputPath);

            // 返回报表文件位置
            return outputPath;
        }

        #region 辅助方法

        /// <summary>
        /// 准备所有需要替换的内容
        /// </summary>
        private static Dictionary<string, string> PrepareReplacements(ReportCalculateNode calculateNode)
        {
            // 替换固定格式信息
            var replacements = new Dictionary<string, string>
            {
                { "{{仪器ID}}", calculateNode.SNCode },
                { "{{仪器型号}}", $"{calculateNode.DeviceConfigInfo.Model}型" },
                { "{{主板软件}}", calculateNode.DevInfo.MainBoardVersion },
                { "{{流路程序}}", calculateNode.DevInfo.FlowVersion },
                { "{{屏幕软件}}", calculateNode.DevInfo.HmiVersion },
                { "{{检验依据}}", calculateNode.DeviceConfigInfo.InspectionBasis },
                { "{{检验时间}}", DateTime.Now.ToString("yyyy.MM.dd") },
                { "{{环境条件}}", FormatTemperatureAndHumidity(calculateNode.Temperature, calculateNode.Humidity) },
                { "{{检验结果}}",GetMainCheckResult(calculateNode) },
                { "{{检验员}}", calculateNode.InspectorName },
                { "{{审核员}}", calculateNode.AuditorName }
            };

            return replacements;
        }

        /// <summary>
        /// 获取总检查结果
        /// 是否所有勾选项都合格
        /// </summary>
        private static string GetMainCheckResult(ReportCalculateNode calculateNode)
        {
            return calculateNode.StandardRangeMeasureData.Any(x => !x.IsQualified) ? "不合格" : "合格";
        }

        /// <summary>
        /// 格式化环境条件显示
        /// </summary>
        private static string FormatTemperatureAndHumidity(double temperature, double humidity)
        {
            string temp = double.IsNaN(temperature) ? " " : $"{temperature:F1}";
            string hum = double.IsNaN(humidity) ? " " : $"{humidity:F1}";
            return $"温度{temp}℃；湿度{hum}%RH";
        }

        /// <summary>
        /// 插入光源信息记录
        /// </summary>
        private static void InsertLightSourceInfo(XWPFDocument doc, ReportCalculateNode calculateNode)
        {
            var position = ReportDocumentHelper.FindMarkerCell(doc, "{{光源信息}}");
            if(!position.HasValue)
            {
                return;
            }

            var table = position.Value.table;
            var startRowIndex = position.Value.rowIndex;

            // 光源信息
            var rows = new List<string[]>
            {
                new[] { "检测光路", $"信号档位：{calculateNode.LightInfo.MainIndex}", "参比光路", $"信号档位：{calculateNode.LightInfo.RefIndex}" },
                new[] { "", $"信号电压1：{calculateNode.LightInfo.MainV1:F4}", "", $"信号电压1：{calculateNode.LightInfo.RefV1:F4}" },
                new[] { "", $"信号电压2：{calculateNode.LightInfo.MainV2:F4}", "", $"信号电压2：{calculateNode.LightInfo.RefV2:F4}" },
                new[] { "", $"信号电压3：{calculateNode.LightInfo.MainV3:F4}", "", $"信号电压3：{calculateNode.LightInfo.RefV3:F4}" },
                new[] { "", $"信号电压4：{calculateNode.LightInfo.MainV4:F4}", "", $"信号电压4：{calculateNode.LightInfo.RefV4:F4}" }
            };

            // 插入数据到表格中
            ReportDocumentHelper.InsertTableRowsWithMerge(table, startRowIndex, rows, 0, 2);

            // 设置整个区域边框
            ReportDocumentHelper.SetBordersForTableRegion(table, startRowIndex, startRowIndex + rows.Count - 1);
        }

        /// <summary>
        /// 插入校准信息记录
        /// </summary>
        private static void InsertCalibrationData(XWPFDocument doc, ReportCalculateNode calculateNode)
        {
            var position = ReportDocumentHelper.FindMarkerCell(doc, "{{校准信息}}");
            if(!position.HasValue) return;

            var table = position.Value.table;
            var startRowIndex = position.Value.rowIndex;

            // 标准量程校准数据
            var stdRows = new List<string[]>
            {
                new[] { "标准量程校准", "量程", $"{calculateNode.StandardRangeCalibrationData.Range:F4}", "通过" },
                new[] { "", "标1", $"{calculateNode.StandardRangeCalibrationData.Standard1Value:F4}", "" },
                new[] { "", "标2", $"{calculateNode.StandardRangeCalibrationData.Standard2Value:F4}",  "" }
            };
            if(calculateNode.DevInfo.IsTNDevice)
            {
                stdRows.Add(new[] { "", "标M", $"{calculateNode.StandardRangeCalibrationData.StandardMValue:F4}", "" });
            }
            stdRows.Add(new[] { "", "K0(截距)", $"{calculateNode.StandardRangeCurveData.B:F4}", "" });
            stdRows.Add(new[] { "", "K1(斜率)", $"{calculateNode.StandardRangeCurveData.K:F4}", "" });
            stdRows.Add(new[] { "", "K2(二次项)", $"{calculateNode.StandardRangeCurveData.A2:F4}", "" });

            // 辅助量程校准数据
            var auxRows = new List<string[]>
            {
                new[] { "辅助量程校准", "量程", $"{calculateNode.AuxiliaryRangeCalibrationData.Range:F4}", "通过" },
                new[] { "", "标1", $"{calculateNode.AuxiliaryRangeCalibrationData.Standard1Value:F4}", "" },
                new[] { "", "标2", $"{calculateNode.AuxiliaryRangeCalibrationData.Standard2Value:F4}", "" }
            };
            if(calculateNode.DevInfo.IsTNDevice)
            {
                auxRows.Add(new[] { "", "标M", $"{calculateNode.AuxiliaryRangeCalibrationData.StandardMValue:F4}", "" });
            }
            auxRows.Add(new[] { "", "K0(截距)", $"{calculateNode.AuxiliaryRangeCurveData.B:F4}", "" });
            auxRows.Add(new[] { "", "K1(斜率)", $"{calculateNode.AuxiliaryRangeCurveData.K:F4}", "" });
            auxRows.Add(new[] { "", "K2(二次项)", $"{calculateNode.AuxiliaryRangeCurveData.A2:F4}", "" });

            // 插入标准量程校准数据到表格中
            ReportDocumentHelper.InsertTableRowsWithMerge(table, startRowIndex, stdRows, 0, 3);

            // 插入辅助量程校准数据到表格中
            ReportDocumentHelper.InsertTableRowsWithMerge(table, startRowIndex + stdRows.Count, auxRows, 0, 3);

            // 设置整个区域边框
            ReportDocumentHelper.SetBordersForTableRegion(table, startRowIndex, startRowIndex + stdRows.Count + auxRows.Count - 1);
        }

        /// <summary>
        /// 插入测量结果记录
        /// </summary>
        private static void InsertMeasureData(XWPFDocument doc, ReportCalculateNode calculateNode)
        {
            var position = ReportDocumentHelper.FindMarkerCell(doc, "{{测量结果}}");
            if(!position.HasValue)
            {
                return;
            }

            // 数据显示精度
            string displayFormat = calculateNode.DeviceConfigInfo.DataAccuracy > 0
                ? $"F{calculateNode.DeviceConfigInfo.DataAccuracy}"
                : "F2"; // 默认两位小数

            var table = position.Value.table;
            var startRowIndex = position.Value.rowIndex;

            // 依次插入标准量程和辅助量程的每个测量项
            int currentRowIndex = startRowIndex;
            foreach(var data in calculateNode.StandardRangeMeasureData)
            {
                int rowCount = data.MeasureDataList.Count;
                var rangeName = "标准量程";
                var firstRowContent = new[]
                {
                    $"{rangeName}{data.MeasureItem.Name}",
                    $"{data.MeasureItem.StandValue.ToString(displayFormat)}",
                    $"{data.MeasureDataList[0].MeasureValue.ToString(displayFormat)}",
                    data.MeasureItem.IsAbsolute ? $"{data.ResultValue:F4}" : $"{data.ResultValue:F2}%",
                    data.MeasureItem.IsAbsolute ? $"±{data.MeasureItem.QualifiedStandard:F4}" : $"±{data.MeasureItem.QualifiedStandard:F2}%",
                    data.IsQualified ? "是" : "否"
                };
                var rows = new List<string[]> { firstRowContent };
                for(int i = 1; i < rowCount; i++)
                {
                    var row = new string[6];
                    row[2] = $"{data.MeasureDataList[i].MeasureValue.ToString(displayFormat)}";
                    rows.Add(row);
                }
                ReportDocumentHelper.InsertTableRowsWithMerge(table, currentRowIndex, rows, 0, 1, 3, 4, 5);
                currentRowIndex += rows.Count;
            }
            foreach(var data in calculateNode.AuxiliaryRangeMeasureData)
            {
                int rowCount = data.MeasureDataList.Count;
                var rangeName = "辅助量程";
                var firstRowContent = new[]
                {
                    $"{rangeName}{data.MeasureItem.Name}",
                    $"{data.MeasureItem.StandValue.ToString(displayFormat)}",
                    $"{data.MeasureDataList[0].MeasureValue.ToString(displayFormat)}",
                    data.MeasureItem.IsAbsolute ? $"{data.ResultValue:F4}" : $"{data.ResultValue:F2}%",
                    data.MeasureItem.IsAbsolute ? $"±{data.MeasureItem.QualifiedStandard:F4}" : $"±{data.MeasureItem.QualifiedStandard:F2}%",
                    data.IsQualified ? "是" : "否"
                };
                var rows = new List<string[]> { firstRowContent };
                for(int i = 1; i < rowCount; i++)
                {
                    var row = new string[6];
                    row[2] = $"{data.MeasureDataList[i].MeasureValue.ToString(displayFormat)}";
                    rows.Add(row);
                }
                ReportDocumentHelper.InsertTableRowsWithMerge(table, currentRowIndex, rows, 0, 1, 3, 4, 5);
                currentRowIndex += rows.Count;
            }

            // 设置整个区域边框
            ReportDocumentHelper.SetBordersForTableRegion(table, startRowIndex, currentRowIndex - 1);
        }

        #endregion
    }
}