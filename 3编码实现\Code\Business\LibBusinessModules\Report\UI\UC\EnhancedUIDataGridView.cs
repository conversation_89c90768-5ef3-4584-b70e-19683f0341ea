using LibBaseModules.Helper;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.Report.UI
{
    /// <summary>
    /// 增强型UIDataGridView用户控件
    /// 功能包括：自动行号、复选框增强（全选/反选）、选中数量统计
    /// </summary>
    public partial class EnhancedUIDataGridView : UIUserControl
    {
        #region 全局常量

        /// <summary>
        /// 选中行列名常量
        /// </summary>
        public static readonly string SELECT_COLUMN_NAME = "Select";

        /// <summary>
        /// 行号列名常量
        /// </summary>
        public static readonly string ROW_NUMBER_COLUMN_NAME = "RowNumber";

        #endregion

        #region 字段属性

        /// <summary>
        /// 是否启用行号列
        /// </summary>
        private bool _showRowNumbers = true;

        /// <summary>
        /// 是否启用复选框功能
        /// </summary>
        private bool _enableCheckBox = true;

        /// <summary>
        /// 是否启用工具栏
        /// </summary>
        private bool _showToolbar = true;

        /// <summary>
        /// 是否已经初始化过行号列显示
        /// </summary>
        private bool _hasInit = false;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取内部DataGridView控件
        /// </summary>
        [Browsable(false)]
        public UIDataGridView DataGridView => dataGridView;

        /// <summary>
        /// 数据源
        /// </summary>
        [Browsable(true)]
        [Category("数据")]
        [Description("数据源")]
        public object DataSource
        {
            get => dataGridView?.DataSource;
            set
            {
                if(dataGridView != null)
                {
                    dataGridView.DataSource = value;
                }
            }
        }

        /// <summary>
        /// 是否显示行号
        /// </summary>
        [Browsable(true)]
        [Category("外观")]
        [Description("是否显示行号列")]
        [DefaultValue(true)]
        public bool ShowRowNumbers
        {
            get => _showRowNumbers;
            set
            {
                _showRowNumbers = value;
                if(dataGridView != null)
                {
                    RefreshRowNumbers();
                }
            }
        }

        /// <summary>
        /// 是否启用复选框
        /// </summary>
        [Browsable(true)]
        [Category("外观")]
        [Description("是否启用复选框功能")]
        [DefaultValue(true)]
        public bool ShowCheckBox
        {
            get => _enableCheckBox;
            set
            {
                _enableCheckBox = value;
                if(dataGridView != null)
                {
                    RefreshCheckBoxColumn();
                }
            }
        }

        /// <summary>
        /// 是否显示工具栏
        /// </summary>
        [Browsable(true)]
        [Category("外观")]
        [Description("是否显示工具栏")]
        [DefaultValue(true)]
        public bool ShowToolbar
        {
            get => _showToolbar;
            set
            {
                _showToolbar = value;
                if(toolbarPanel != null)
                {
                    toolbarPanel.Visible = value;
                }
            }
        }

        /// <summary>
        /// 获取选中的行数量
        /// </summary>
        [Browsable(false)]
        public int SelectedRowCount
        {
            get
            {
                if(dataGridView == null || !_enableCheckBox) return 0;

                int checkBoxColumnIndex = GetCheckBoxColumnIndex();
                if(checkBoxColumnIndex < 0) return 0;

                return dataGridView.Rows.Cast<DataGridViewRow>()
                    .Count(row => Convert.ToBoolean(row.Cells[checkBoxColumnIndex]?.Value ?? false));
            }
        }

        /// <summary>
        /// 获取总行数
        /// </summary>
        [Browsable(false)]
        public int TotalRowCount => dataGridView?.Rows.Count ?? 0;

        #endregion

        #region 构造函数

        public EnhancedUIDataGridView()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 绑定DataGridView事件
            dataGridView.DataSourceChanged += DataGridView_DataSourceChanged;
            dataGridView.CellValueChanged += DataGridView_CellValueChanged;
            dataGridView.CurrentCellDirtyStateChanged += DataGridView_CurrentCellDirtyStateChanged;

            // 绑定按钮事件
            btnSelectAll.Click += BtnSelectAll_Click;
            btnInvertSelection.Click += BtnInvertSelection_Click;

            // 绑定控件可见性变化事件，用于处理TabPage激活时的行号更新
            this.VisibleChanged += EnhancedUIDataGridView_VisibleChanged;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 数据源改变事件
        /// </summary>
        private void DataGridView_DataSourceChanged(object sender, EventArgs e)
        {
            // 刷新行号和选择框显示
            RefreshColumns();

            // 数据绑定完成后更新行号和选中数量
            UpdateRowNumbers();
            UpdateSelectionCount();
        }

        /// <summary>
        /// 单元格值改变事件
        /// </summary>
        private void DataGridView_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if(e.ColumnIndex == GetCheckBoxColumnIndex())
            {
                UpdateSelectionCount();
            }
        }

        /// <summary>
        /// 当前单元格脏状态改变事件（用于复选框实时更新）
        /// </summary>
        private void DataGridView_CurrentCellDirtyStateChanged(object sender, EventArgs e)
        {
            if(dataGridView.IsCurrentCellDirty && dataGridView.CurrentCell is DataGridViewCheckBoxCell)
            {
                dataGridView.CommitEdit(DataGridViewDataErrorContexts.Commit);
            }
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void BtnSelectAll_Click(object sender, EventArgs e)
        {
            SelectAll(true);
        }

        /// <summary>
        /// 反选按钮点击事件
        /// </summary>
        private void BtnInvertSelection_Click(object sender, EventArgs e)
        {
            InvertSelection();
        }

        /// <summary>
        /// 控件可见性变化事件 - 处理TabPage激活时的行号更新
        /// </summary>
        private void EnhancedUIDataGridView_VisibleChanged(object sender, EventArgs e)
        {
            if(!this._hasInit && this.Visible && _showRowNumbers)
            {
                // 当控件变为可见时，重新更新行号
                UpdateRowNumbers();
                this._hasInit = true;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置列标题从属性描述
        /// </summary>
        /// <param name="type">数据类型</param>
        public void SetColumnHeadersFromDescriptions(Type type)
        {
            DataGridViewHelper.SetColumnHeadersFromDescriptions(dataGridView, type);
        }

        /// <summary>
        /// 隐藏指定列
        /// </summary>
        /// <param name="columnName">列名</param>
        public void HideColumn(string columnName)
        {
            DataGridViewHelper.HideSpecifyColumn(dataGridView, columnName);
        }

        /// <summary>
        /// 全选或取消全选
        /// </summary>
        /// <param name="select">true为全选，false为取消全选</param>
        public void SelectAll(bool select = true)
        {
            if(dataGridView == null || !_enableCheckBox) return;

            int checkBoxColumnIndex = GetCheckBoxColumnIndex();
            if(checkBoxColumnIndex < 0) return;

            foreach(DataGridViewRow row in dataGridView.Rows)
            {
                row.Cells[checkBoxColumnIndex].Value = select;
            }

            UpdateSelectionCount();
        }

        /// <summary>
        /// 反选
        /// </summary>
        public void InvertSelection()
        {
            if(dataGridView == null || !_enableCheckBox) return;

            int checkBoxColumnIndex = GetCheckBoxColumnIndex();
            if(checkBoxColumnIndex < 0) return;

            foreach(DataGridViewRow row in dataGridView.Rows)
            {
                bool currentValue = Convert.ToBoolean(row.Cells[checkBoxColumnIndex].Value ?? false);
                row.Cells[checkBoxColumnIndex].Value = !currentValue;
            }

            UpdateSelectionCount();
        }

        /// <summary>
        /// 获取选中的行
        /// </summary>
        /// <returns>选中的行集合</returns>
        public List<DataGridViewRow> GetSelectedRows()
        {
            if(dataGridView == null || !_enableCheckBox) return new List<DataGridViewRow>();

            int checkBoxColumnIndex = GetCheckBoxColumnIndex();
            if(checkBoxColumnIndex < 0) return new List<DataGridViewRow>();

            return dataGridView.Rows.Cast<DataGridViewRow>()
                .Where(row => Convert.ToBoolean(row.Cells[checkBoxColumnIndex]?.Value ?? false))
                .ToList();
        }

        /// <summary>
        /// 获取选中行的数据对象
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <returns>选中的数据对象集合</returns>
        public List<T> GetSelectedData<T>() where T : class
        {
            return GetSelectedRows().Select(row => row.DataBoundItem as T)
                .Where(item => item != null)
                .ToList();
        }

        /// <summary>
        /// 程序化勾选或取消勾选指定行的复选框
        /// </summary>
        /// <param name="rowIndex">行索引（从0开始）</param>
        /// <param name="selected">true为勾选，false为取消勾选</param>
        /// <returns>操作是否成功</returns>
        public bool SetRowSelected(int rowIndex, bool selected)
        {
            if(dataGridView == null || !_enableCheckBox) return false;

            // 检查行索引是否有效
            if(rowIndex < 0 || rowIndex >= dataGridView.Rows.Count) return false;

            int checkBoxColumnIndex = GetCheckBoxColumnIndex();
            if(checkBoxColumnIndex < 0) return false;

            try
            {
                dataGridView.Rows[rowIndex].Cells[checkBoxColumnIndex].Value = selected;
                UpdateSelectionCount();
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新列结构
        /// </summary>
        private void RefreshColumns()
        {
            if(dataGridView?.DataSource == null) return;

            // 按正确顺序添加列： 复选框列 -> 行号列 ->数据列
            RefreshCheckBoxColumn();
            RefreshRowNumbers();
        }

        /// <summary>
        /// 刷新复选框列
        /// </summary>
        private void RefreshCheckBoxColumn()
        {
            if(dataGridView == null) return;

            if(_enableCheckBox)
            {
                // 检查是否已存在复选框列
                bool hasCheckBoxColumn = dataGridView.Columns.Cast<DataGridViewColumn>()
                    .Any(col => col is DataGridViewCheckBoxColumn && col.Name == SELECT_COLUMN_NAME);

                if(!hasCheckBoxColumn)
                {
                    // 创建复选框列
                    DataGridViewCheckBoxColumn checkBoxColumn = new DataGridViewCheckBoxColumn
                    {
                        Name = SELECT_COLUMN_NAME,
                        HeaderText = "选择",
                        Width = 50,
                        ReadOnly = false,
                        Frozen = true
                    };

                    // 添加复选框列最前面
                    dataGridView.Columns.Insert(0, checkBoxColumn);
                }
            }
            else
            {
                // 移除复选框列
                var checkBoxColumn = dataGridView.Columns.Cast<DataGridViewColumn>()
                    .FirstOrDefault(col => col is DataGridViewCheckBoxColumn && col.Name == SELECT_COLUMN_NAME);
                if(checkBoxColumn != null)
                {
                    dataGridView.Columns.Remove(checkBoxColumn);
                }
            }
        }

        /// <summary>
        /// 刷新行号
        /// </summary>
        private void RefreshRowNumbers()
        {
            if(dataGridView == null) return;

            if(_showRowNumbers)
            {
                // 检查是否已存在行号列
                bool hasRowNumberColumn = dataGridView.Columns.Cast<DataGridViewColumn>()
                    .Any(col => col.Name == ROW_NUMBER_COLUMN_NAME);

                if(!hasRowNumberColumn)
                {
                    // 计算行号列应该插入的位置（复选框列之后）
                    int insertIndex = GetColumnIndex(SELECT_COLUMN_NAME) >= 0 ? 1 : 0;

                    var rowNumberColumn = new DataGridViewTextBoxColumn
                    {
                        Name = ROW_NUMBER_COLUMN_NAME,
                        HeaderText = "序号",
                        Width = 50,
                        ReadOnly = true,
                        SortMode = DataGridViewColumnSortMode.NotSortable,
                        Frozen = true
                    };

                    // 添加行号
                    if(insertIndex < dataGridView.Columns.Count)
                    {
                        dataGridView.Columns.Insert(insertIndex, rowNumberColumn);
                    }
                    else
                    {
                        dataGridView.Columns.Add(rowNumberColumn);
                    }
                }

                // 更新行号值
                UpdateRowNumbers();
            }
            else
            {
                // 移除行号列
                var rowNumberColumn = dataGridView.Columns.Cast<DataGridViewColumn>()
                    .FirstOrDefault(col => col.Name == ROW_NUMBER_COLUMN_NAME);
                if(rowNumberColumn != null)
                {
                    dataGridView.Columns.Remove(rowNumberColumn);
                }
            }
        }

        /// <summary>
        /// 更新行号值
        /// </summary>
        private void UpdateRowNumbers()
        {
            if(dataGridView == null || !_showRowNumbers) return;

            var rowNumberColumn = dataGridView.Columns.Cast<DataGridViewColumn>()
                .FirstOrDefault(col => col.Name == ROW_NUMBER_COLUMN_NAME);
            if(rowNumberColumn == null) return;

            // 检查控件是否已经完全初始化
            if(!dataGridView.IsHandleCreated)
            {
                // 如果句柄未创建，强制创建句柄
                try
                {
                    var handle = dataGridView.Handle; // 这会触发句柄创建
                }
                catch
                {
                    // 如果创建句柄失败，延迟更新
                    this.BeginInvoke(new Action(UpdateRowNumbers));
                    return;
                }
            }

            // 检查是否在TabPage中且TabPage未激活
            if(this.Parent is TabPage tabPage && tabPage.Parent is TabControl tabControl)
            {
                if(tabControl.SelectedTab != tabPage)
                {
                    // TabPage未激活，但仍然尝试更新行号
                    // 这里使用Invoke确保在UI线程中执行
                    if(this.InvokeRequired)
                    {
                        this.Invoke(new Action(() => UpdateRowNumbersCore(rowNumberColumn.Index)));
                    }
                    else
                    {
                        UpdateRowNumbersCore(rowNumberColumn.Index);
                    }
                    return;
                }
            }

            // 正常情况下更新行号
            UpdateRowNumbersCore(rowNumberColumn.Index);
        }

        /// <summary>
        /// 核心行号更新逻辑
        /// </summary>
        /// <param name="rowNumberColumnIndex">行号列索引</param>
        private void UpdateRowNumbersCore(int rowNumberColumnIndex)
        {
            try
            {
                for(int i = 0; i < dataGridView.Rows.Count; i++)
                {
                    if(dataGridView.Rows[i].Cells[rowNumberColumnIndex] != null)
                    {
                        dataGridView.Rows[i].Cells[rowNumberColumnIndex].Value = (i + 1).ToString();
                    }
                }

                // 强制刷新显示
                if(dataGridView.IsHandleCreated)
                {
                    dataGridView.Invalidate();
                }
            }
            catch(Exception ex)
            {
                // 记录错误但不抛出异常，避免影响主流程
                System.Diagnostics.Debug.WriteLine($"UpdateRowNumbersCore error: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取复选框列索引
        /// </summary>
        /// <returns>复选框列索引，如果不存在返回-1</returns>
        public int GetCheckBoxColumnIndex()
        {
            if(dataGridView == null) return -1;

            var checkBoxColumn = dataGridView.Columns.Cast<DataGridViewColumn>()
                .FirstOrDefault(col => col is DataGridViewCheckBoxColumn && col.Name == SELECT_COLUMN_NAME);
            return checkBoxColumn?.Index ?? -1;
        }

        /// <summary>
        /// 获取列索引
        /// </summary>
        /// <returns>列索引，如果不存在返回-1</returns>
        private int GetColumnIndex(string columnName)
        {
            if(dataGridView == null) return -1;

            var rowNumberColumn = dataGridView.Columns.Cast<DataGridViewColumn>()
                .FirstOrDefault(col => col.Name == columnName);
            return rowNumberColumn?.Index ?? -1;
        }

        /// <summary>
        /// 更新选中数量显示
        /// </summary>
        private void UpdateSelectionCount()
        {
            if(lblSelectionCount != null)
            {
                int selectedCount = SelectedRowCount;
                int totalCount = TotalRowCount;
                lblSelectionCount.Text = $"已选中 {selectedCount}/{totalCount} 项";
            }
        }

        #endregion
    }
}
