﻿using LibBusinessModules.Config;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.Report.Config;
using SqlSugar;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LibBusinessModules.Report.UI
{
    /// <summary>
    /// 报表数据展示控件
    /// </summary>
    public partial class UC_ReportDataShow : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 报表导出数据
        /// </summary>
        private DeviceRawReportData _reportData;

        /// <summary>
        /// 设备配置
        /// </summary>
        DeviceConfig _deviceConfig;

        #endregion

        #region 构造

        public UC_ReportDataShow()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_ReportDataShow_Load(object sender, EventArgs e)
        {
            var nameList = SystemConfig.GetInstance().GetAllEmployeeInfo().Select(x => x.Name).ToList();
            // 初始化审核员和检验员下拉框
            cmbAuditor.DataSource = nameList;
            cmbInspector.DataSource = nameList;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 加载数据
        /// </summary>
        /// <param name="reportData"></param>
        public void LoadReportData(DeviceRawReportData reportData)
        {
            _reportData = reportData;
            if(_reportData != null)
            {
                // 获取设备配置信息
                _deviceConfig = DeviceManager.GetInstance().GetDeviceConfig(_reportData.SNCode);

                // 设备序列号显示
                txtSNCode.Text = _reportData.SNCode;

                // 设备配置信息显示
                if(_deviceConfig != null)
                {
                    txtFactor.Text = _deviceConfig.Factor;
                    txtIdCode.Text = _deviceConfig.IdCode;
                    txtModel.Text = _deviceConfig.Model;
                    txtInspectionBasis.Text = _deviceConfig.InspectionBasis;
                    txtStandardRange.Text = _deviceConfig.StandardRange.ToString("F2");
                    lblStandardRangeUnit.Text = _deviceConfig.StandardRangeUnit;
                    txtAuxiliaryRange.Text = _deviceConfig.AuxiliaryRange.ToString("F2");
                    lblAuxiliaryRangeUnit.Text = _deviceConfig.AuxiliaryRangeUnit;
                }

                // 设备基本信息显示
                if(_reportData.DevInfo != null)
                {
                    txtMainBoardVersion.Text = _reportData.DevInfo.MainBoardVersion;
                    txtHmiVersion.Text = _reportData.DevInfo.HmiVersion;
                    txtFlowVersion.Text = _reportData.DevInfo.FlowVersion;
                    txtPumpVersion.Text = _reportData.DevInfo.PumpVersion;
                    txtPumpSN.Text = _reportData.DevInfo.PumpSN;
                    txtValveVersion.Text = _reportData.DevInfo.ValveVersion;
                    txtValveSN.Text = _reportData.DevInfo.ValveSN;
                }

                // 光源信息显示
                if(_reportData.LightInfo != null)
                {
                    txtMainIndex.Text = _reportData.LightInfo.MainIndex.ToString();
                    txtMainV1.Text = _reportData.LightInfo.MainV1.ToString("F4");
                    txtMainV2.Text = _reportData.LightInfo.MainV2.ToString("F4");
                    txtMainV3.Text = _reportData.LightInfo.MainV3.ToString("F4");
                    txtMainV4.Text = _reportData.LightInfo.MainV4.ToString("F4");
                    txtRefIndex.Text = _reportData.LightInfo.RefIndex.ToString();
                    txtRefV1.Text = _reportData.LightInfo.RefV1.ToString("F4");
                    txtRefV2.Text = _reportData.LightInfo.RefV2.ToString("F4");
                    txtRefV3.Text = _reportData.LightInfo.RefV3.ToString("F4");
                    txtRefV4.Text = _reportData.LightInfo.RefV4.ToString("F4");
                }

                // 测量数据、校准数据显示
                if(_reportData.DevInfo != null)
                {
                    // 高指
                    if(_reportData.DevInfo.IsIMNDevice)
                    {
                        dgvMeasureData.SetColumnHeadersFromDescriptions(typeof(ImnMeasureData));
                        dgvCalibrationData.SetColumnHeadersFromDescriptions(typeof(ImnCalibrationData));

                        dgvMeasureData.DataSource = _reportData.ImnMeasureDataList;
                        dgvCalibrationData.DataSource = _reportData.ImnCalibrationDataList;
                    }
                    // 总氮
                    else if(_reportData.DevInfo.IsTNDevice)
                    {
                        dgvMeasureData.DataSource = _reportData.TnMeasureDataList;
                        dgvCalibrationData.DataSource = _reportData.TnCalibrationDataList;

                        dgvMeasureData.SetColumnHeadersFromDescriptions(typeof(TnMeasureData));
                        dgvCalibrationData.SetColumnHeadersFromDescriptions(typeof(TnCalibrationData));
                    }
                    // 常规
                    else
                    {
                        dgvMeasureData.DataSource = _reportData.MeasureDataList;
                        dgvCalibrationData.DataSource = _reportData.CalibrationDataList;

                        dgvMeasureData.SetColumnHeadersFromDescriptions(typeof(MeasureData));
                        dgvCalibrationData.SetColumnHeadersFromDescriptions(typeof(CalibrationData));
                    }
                }

                // 曲线数据显示
                dgvCurveData.DataSource = _reportData.CurveDataList;
                dgvCurveData.SetColumnHeadersFromDescriptions(typeof(CurveData));

                // 隐藏序列号列
                dgvMeasureData.HideColumn("SNCode");
                dgvCalibrationData.HideColumn("SNCode");
                dgvCurveData.HideColumn("SNCode");

#if TEST
                #region 测试模式下，自动勾选dgvMeasureData所有行

                // 使用 EnhancedUIDataGridView 的全选功能
                dgvMeasureData.SelectAll(true);

                #endregion

                #region 测试模式下，按照取数规则，自动在dgvCalibrationData控件中勾选数据

                // 获取标准量程和辅助量程的校准数据
                var standardRangeCalibrations = dgvCalibrationData.DataGridView.Rows.Cast<DataGridViewRow>()
                    .Select(row => row.DataBoundItem)
                    .Where(data =>
                    {
                        if(_reportData.DevInfo.IsIMNDevice)
                        {
                            var calibrationData = (ImnCalibrationData)data;
                            return Math.Abs(calibrationData.Range - _deviceConfig.StandardRange) < 0.0001;
                        }
                        else if(_reportData.DevInfo.IsTNDevice)
                        {
                            var calibrationData = (TnCalibrationData)data;
                            return Math.Abs(calibrationData.Range - _deviceConfig.StandardRange) < 0.0001;
                        }
                        else
                        {
                            var calibrationData = (CalibrationData)data;
                            return Math.Abs(calibrationData.Range - _deviceConfig.StandardRange) < 0.0001;
                        }
                    })
                    .ToList();

                var auxiliaryRangeCalibrations = dgvCalibrationData.DataGridView.Rows.Cast<DataGridViewRow>()
                    .Select(row => row.DataBoundItem)
                    .Where(data =>
                    {
                        if(_reportData.DevInfo.IsIMNDevice)
                        {
                            var calibrationData = (ImnCalibrationData)data;
                            return Math.Abs(calibrationData.Range - _deviceConfig.AuxiliaryRange) < 0.0001;
                        }
                        else if(_reportData.DevInfo.IsTNDevice)
                        {
                            var calibrationData = (TnCalibrationData)data;
                            return Math.Abs(calibrationData.Range - _deviceConfig.AuxiliaryRange) < 0.0001;
                        }
                        else
                        {
                            var calibrationData = (CalibrationData)data;
                            return Math.Abs(calibrationData.Range - _deviceConfig.AuxiliaryRange) < 0.0001;
                        }
                    })
                    .ToList();

                // 勾选标准量程的校准数据
                foreach(DataGridViewRow row in dgvCalibrationData.DataGridView.Rows)
                {
                    var data = row.DataBoundItem;
                    if(_reportData.DevInfo.IsIMNDevice)
                    {
                        var calibrationData = (ImnCalibrationData)data;
                        if(Math.Abs(calibrationData.Range - _deviceConfig.StandardRange) < 0.0001)
                        {
                            // 对于每个类型，只选择一条数据
                            var isFirstOfType = standardRangeCalibrations
                                .Cast<ImnCalibrationData>()
                                .Where(x => x.Type?.Trim() == calibrationData.Type?.Trim())
                                .FirstOrDefault() == calibrationData;
                            dgvCalibrationData.SetRowSelected(row.Index, isFirstOfType);
                        }
                    }
                    else if(_reportData.DevInfo.IsTNDevice)
                    {
                        var calibrationData = (TnCalibrationData)data;
                        if(Math.Abs(calibrationData.Range - _deviceConfig.StandardRange) < 0.0001)
                        {
                            // 对于每个类型，只选择一条数据
                            var isFirstOfType = standardRangeCalibrations
                                .Cast<TnCalibrationData>()
                                .Where(x => x.Type?.Trim() == calibrationData.Type?.Trim())
                                .FirstOrDefault() == calibrationData;
                            dgvCalibrationData.SetRowSelected(row.Index, isFirstOfType);
                        }
                    }
                    else
                    {
                        var calibrationData = (CalibrationData)data;
                        if(Math.Abs(calibrationData.Range - _deviceConfig.StandardRange) < 0.0001)
                        {
                            // 对于每个类型，只选择一条数据
                            var isFirstOfType = standardRangeCalibrations
                                .Cast<CalibrationData>()
                                .Where(x => x.Type?.Trim() == calibrationData.Type?.Trim())
                                .FirstOrDefault() == calibrationData;
                            dgvCalibrationData.SetRowSelected(row.Index, isFirstOfType);
                        }
                    }
                }

                // 勾选辅助量程的校准数据
                foreach(DataGridViewRow row in dgvCalibrationData.DataGridView.Rows)
                {
                    var data = row.DataBoundItem;
                    if(_reportData.DevInfo.IsIMNDevice)
                    {
                        var calibrationData = (ImnCalibrationData)data;
                        if(Math.Abs(calibrationData.Range - _deviceConfig.AuxiliaryRange) < 0.0001)
                        {
                            // 对于每个类型，只选择一条数据
                            var isFirstOfType = auxiliaryRangeCalibrations
                                .Cast<ImnCalibrationData>()
                                .Where(x => x.Type?.Trim() == calibrationData.Type?.Trim())
                                .FirstOrDefault() == calibrationData;
                            dgvCalibrationData.SetRowSelected(row.Index, isFirstOfType);
                        }
                    }
                    else if(_reportData.DevInfo.IsTNDevice)
                    {
                        var calibrationData = (TnCalibrationData)data;
                        if(Math.Abs(calibrationData.Range - _deviceConfig.AuxiliaryRange) < 0.0001)
                        {
                            // 对于每个类型，只选择一条数据
                            var isFirstOfType = auxiliaryRangeCalibrations
                                .Cast<TnCalibrationData>()
                                .Where(x => x.Type?.Trim() == calibrationData.Type?.Trim())
                                .FirstOrDefault() == calibrationData;
                            dgvCalibrationData.SetRowSelected(row.Index, isFirstOfType);
                        }
                    }
                    else
                    {
                        var calibrationData = (CalibrationData)data;
                        if(Math.Abs(calibrationData.Range - _deviceConfig.AuxiliaryRange) < 0.0001)
                        {
                            // 对于每个类型，只选择一条数据
                            var isFirstOfType = auxiliaryRangeCalibrations
                                .Cast<CalibrationData>()
                                .Where(x => x.Type?.Trim() == calibrationData.Type?.Trim())
                                .FirstOrDefault() == calibrationData;
                            dgvCalibrationData.SetRowSelected(row.Index, isFirstOfType);
                        }
                    }
                }

                #endregion

                #region 测试模式下，按照取数规则，自动在dgvCurveData控件中勾选数据

                // 获取标准量程和辅助量程的曲线数据
                var standardRangeCurves = dgvCurveData.DataGridView.Rows.Cast<DataGridViewRow>()
                    .Select(row => (CurveData)row.DataBoundItem)
                    .Where(curve => Math.Abs(curve.Range - _deviceConfig.StandardRange) < 0.0001)
                    .ToList();

                var auxiliaryRangeCurves = dgvCurveData.DataGridView.Rows.Cast<DataGridViewRow>()
                    .Select(row => (CurveData)row.DataBoundItem)
                    .Where(curve => Math.Abs(curve.Range - _deviceConfig.AuxiliaryRange) < 0.0001)
                    .ToList();

                // 勾选标准量程和辅助量程的曲线数据
                foreach(DataGridViewRow row in dgvCurveData.DataGridView.Rows)
                {
                    var curveData = (CurveData)row.DataBoundItem;
                    if(Math.Abs(curveData.Range - _deviceConfig.StandardRange) < 0.0001)
                    {
                        // 只选择标准量程的一条数据
                        bool shouldSelect = standardRangeCurves.FirstOrDefault() == curveData;
                        dgvCurveData.SetRowSelected(row.Index, shouldSelect);
                    }
                    else if(Math.Abs(curveData.Range - _deviceConfig.AuxiliaryRange) < 0.0001)
                    {
                        // 只选择辅助量程的一条数据
                        bool shouldSelect = auxiliaryRangeCurves.FirstOrDefault() == curveData;
                        dgvCurveData.SetRowSelected(row.Index, shouldSelect);
                    }
                }

                #endregion
#endif

                // 数据导出时检查设备配置信息是否合法
                if(_deviceConfig == null)
                {
                    throw new Exception($"当前序列号[{_reportData.SNCode}]对应的设备配置信息不存在，请先在[系统配置-因子信息管理]中进行配置！");
                }

                // 统计并显示数据需求
                CalculateAndDisplayDataRequirements();
            }
        }

        /// <summary>
        /// 获取报表计算所需数据
        /// </summary>
        /// <returns></returns>
        public ReportCalculateNode GetReportCalculateData()
        {
            // 数据导出时检查设备配置信息是否合法
            if(_deviceConfig == null)
            {
                throw new Exception($"当前序列号[{_reportData.SNCode}]对应的设备配置信息不存在，请先在[系统配置-因子信息管理]中进行配置！");
            }

            var calculateNode = new ReportCalculateNode(_reportData.SNCode);
            calculateNode.DevInfo = _reportData.DevInfo;
            calculateNode.LightInfo = _reportData.LightInfo;
            calculateNode.DeviceConfigInfo = _deviceConfig;

            #region 辅助数据

            if(double.TryParse(txtTemp.Text, out double tmp))
            {
                calculateNode.Temperature = tmp;
            }
            else
            {
                calculateNode.Temperature = double.NaN;
                //throw new Exception("环境温度输入错误，请检查！");
            }

            if(double.TryParse(txtHumidity.Text, out double humidity) && humidity > 0 && humidity < 100)
            {
                calculateNode.Humidity = humidity;
            }
            else
            {
                calculateNode.Humidity = double.NaN;
                //throw new Exception("环境温度输入错误，请检查！");
            }

            calculateNode.AuditorName = cmbAuditor.Text;
            calculateNode.InspectorName = cmbInspector.Text;

            #endregion

#if TEST
            #region 测试模式下，生成模拟测量数据

            ReportCalculateNode.GenerateMockMeasureData(calculateNode);

            #endregion

#else
            // 测量数据
            var measureData = GetSelectedMeasureData();
            calculateNode.StandardRangeMeasureData = measureData.standardRangeMeasureData;
            calculateNode.AuxiliaryRangeMeasureData = measureData.auxiliaryRangeMeasureData;
#endif

            // 校准数据
            var calibrationData = GetSelectedCalibrationData();
            calculateNode.StandardRangeCalibrationData = calibrationData.standardRangeCalibration;
            calculateNode.AuxiliaryRangeCalibrationData = calibrationData.auxiliaryRangeCalibration;

            // 曲线数据
            var curveData = GetSelectedRangeCurveData();
            calculateNode.StandardRangeCurveData = curveData.standardRangeCurveData;
            calculateNode.AuxiliaryRangeCurveData = curveData.auxiliaryRangeCurveData;

            return calculateNode;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 计算并显示数据需求统计
        /// </summary>
        private void CalculateAndDisplayDataRequirements()
        {
            if(_deviceConfig == null)
                return;

            var requirements = new List<string>();

            // 统计标准量程数据需求
            var standardRangeRequirement = CalculateRangeDataRequirement(_deviceConfig.StandardRangeMeasureItems);
            if(standardRangeRequirement.maxDataCount > 0)
            {
                requirements.Add($"标准量程({_deviceConfig.StandardRange:F2}{_deviceConfig.StandardRangeUnit}): {standardRangeRequirement.maxDataCount}条");
                if(standardRangeRequirement.enabledItems.Count > 0)
                {
                    requirements.Add("已启用测试项:");
                    requirements.Add($"{string.Join("、", standardRangeRequirement.enabledItems)}");
                }
            }

            // 统计辅助量程数据需求
            var auxiliaryRangeRequirement = CalculateRangeDataRequirement(
                _deviceConfig.AuxiliaryRangeMeasureItems);
            if(auxiliaryRangeRequirement.maxDataCount > 0)
            {
                requirements.Add($"辅助量程({_deviceConfig.AuxiliaryRange:F2}{_deviceConfig.AuxiliaryRangeUnit}): {auxiliaryRangeRequirement.maxDataCount}条");
                if(auxiliaryRangeRequirement.enabledItems.Count > 0)
                {
                    requirements.Add("已启用测试项:");
                    requirements.Add($"{string.Join("、", auxiliaryRangeRequirement.enabledItems)}");
                }
            }

            // 显示统计结果
            if(requirements.Count > 0)
            {
                txtDataNeedSelect.Text = string.Join("\n", requirements);
            }
            else
            {
                txtDataNeedSelect.Text = "未启用任何测试项";
            }
        }

        /// <summary>
        /// 计算指定量程的数据需求
        /// </summary>
        /// <param name="measureItems">测试项列表</param>
        /// <returns>数据需求统计结果</returns>
        private (int maxDataCount, List<string> enabledItems) CalculateRangeDataRequirement(
            List<MeasureItem> measureItems)
        {
            var enabledItems = new List<string>();
            int maxDataCount = 0;

            // 遍历所有启用的测试项
            foreach(var item in measureItems.Where(x => x.IsUsed))
            {
                enabledItems.Add(item.Name);

                // 取最大数据需求量（同一量程有多个测试项时，数据可复用，取最大值）
                if(item.CalculableDataCount > maxDataCount)
                {
                    maxDataCount = item.CalculableDataCount;
                }
            }

            return (maxDataCount, enabledItems);
        }

        /// <summary>
        /// 获取选中的标准量程和辅助量程测量数据
        /// </summary>
        /// <returns>标准量程和辅助量程的测量数据，按测量项做区分</returns>
        private (List<ReportMeasureData> standardRangeMeasureData, List<ReportMeasureData> auxiliaryRangeMeasureData) GetSelectedMeasureData()
        {
            // 获取所有已勾选的行并转换为ReportTmpMeasureData
            var reportDataList = dgvMeasureData.GetSelectedRows()
                .Select(r =>
                {
                    var boundItem = r.DataBoundItem;
                    if(_reportData.DevInfo.IsIMNDevice)
                    {
                        var data = (ImnMeasureData)boundItem;
                        return data.Type?.Trim() == "测量" ? new ReportTmpMeasureData
                        {
                            Time = data.Time,
                            MeasureValue = data.Value,
                            Range = data.Range
                        } : null;
                    }
                    else if(_reportData.DevInfo.IsTNDevice)
                    {
                        var data = (TnMeasureData)boundItem;
                        return data.Type?.Trim() == "测量" ? new ReportTmpMeasureData
                        {
                            Time = data.Time,
                            MeasureValue = data.Value,
                            Range = data.Range
                        } : null;
                    }
                    else
                    {
                        var data = (MeasureData)boundItem;
                        return data.Type?.Trim() == "测量" ? new ReportTmpMeasureData
                        {
                            Time = data.Time,
                            MeasureValue = data.Value,
                            Range = data.Range
                        } : null;
                    }
                })
                .Where(data => data != null)  // 过滤掉不符合Type条件的数据
                .ToList();

            if(reportDataList.Count == 0)
            {
                throw new Exception("未选择任何类型为测量的测量数据！");
            }

            // 获取标准量程数据
            var standardRangeMeasureData = GetMeasureDataByRange(_deviceConfig.StandardRangeMeasureItems, _deviceConfig.StandardRange, "标准量程", reportDataList);

            // 获取辅助量程数据
            var auxiliaryRangeMeasureData = GetMeasureDataByRange(_deviceConfig.AuxiliaryRangeMeasureItems, _deviceConfig.AuxiliaryRange, "辅助量程", reportDataList);

            return (standardRangeMeasureData, auxiliaryRangeMeasureData);
        }

        /// <summary>
        /// 根据量程获取测量数据
        /// </summary>
        /// <param name="measureItems">测量项列表</param>
        /// <param name="range">量程值</param>
        /// <param name="rangeName">量程名称</param>
        /// <param name="reportDataList">报表数据列表</param>
        /// <returns>测量数据列表</returns>
        private List<ReportMeasureData> GetMeasureDataByRange(List<MeasureItem> measureItems, double range, string rangeName, List<ReportTmpMeasureData> reportDataList)
        {
            List<ReportMeasureData> measureDataList = new List<ReportMeasureData>();

            // 符合量程漂移量程、浓度要求的数据缓存
            List<ReportTmpMeasureData> rangeDriftTmpDataList = new List<ReportTmpMeasureData>();
            // 量程漂移结果数据缓存
            ReportMeasureData rangeDriftData = null;
            // 零点漂移结果数据缓存
            ReportMeasureData zeroPointDriftData = null;

            // 检查测试项
            foreach(var item in measureItems.Where(x => x.IsUsed))
            {
                // 此项数据取值范围
                double dataRange = item.ValueRange * 0.01;

                // 获取该测试项对应浓度值的所有数据
                var matchingData = reportDataList
                    // 量程为指定量程
                    .Where(data => Math.Abs(data.Range - range) < 0.0001)
                    // 浓度值为当前测量项对应浓度值附近指定范围内的数据
                    .Where(data => Math.Abs(data.MeasureValue - item.StandValue) < item.StandValue * dataRange)
                    .ToList();

                if(matchingData.Count < item.CalculableDataCount)
                {
                    throw new Exception($"{rangeName}[{range:F2}]的[{item.Name}]测试项已启用，标准浓度值为[{item.StandValue:F2}]，需要{item.CalculableDataCount}个数据点，当前仅选择了{matchingData.Count}个！");
                }

                var measureData = new ReportMeasureData();
                measureData.MeasureItem = item;
                measureData.Range = range;

                // 如果是零点漂移测试项，记录数据
                if(item.ItemType == eMeasureItemType.ZeroPointDrift)
                {
                    measureData.MeasureDataList = matchingData.Take(item.CalculableDataCount).ToList();
                    zeroPointDriftData = measureData;
                    measureDataList.Add(measureData);
                }
                // 如果是量程漂移测试项，先保存原始数据
                else if(item.ItemType == eMeasureItemType.MeasurementRangeDrift)
                {
                    rangeDriftTmpDataList = matchingData;
                    rangeDriftData = measureData;
                }
                // 其他测试项直接处理
                else
                {
                    measureData.MeasureDataList = matchingData.Take(item.CalculableDataCount).ToList();
                    measureDataList.Add(measureData);
                }
            }

            // 量程漂移有原始数据，证明启用了这项。处理量程漂移数据
            if(rangeDriftData != null && rangeDriftTmpDataList.Any())
            {
                if(zeroPointDriftData == null)
                {
                    throw new Exception($"{rangeName}[{range:F2}]的量程漂移测试项需要零点漂移数据，但未找到！");
                }

                {
                    // 获取零点漂移前的3条数据（取时间最晚的3条）
                    var beforeData = rangeDriftTmpDataList
                        .Where(data => data.Time < zeroPointDriftData.MeasureDataList.Min(x => x.Time))
                        .OrderByDescending(data => data.Time)
                        .Take(3)
                        .ToList();

                    if(beforeData.Count < 3)
                    {
                        throw new Exception($"{rangeName}[{range:F2}]的量程漂移测试项需要零点漂移前的3条数据，但仅找到{beforeData.Count}条！");
                    }

                    // 获取零点漂移后的3条数据（取时间最早的3条）
                    var afterData = rangeDriftTmpDataList
                        .Where(data => data.Time > zeroPointDriftData.MeasureDataList.Max(x => x.Time))
                        .OrderBy(data => data.Time)
                        .Take(3)
                        .ToList();

                    if(afterData.Count < 3)
                    {
                        throw new Exception($"{rangeName}[{range:F2}]的量程漂移测试项需要零点漂移后的3条数据，但仅找到{afterData.Count}条！");
                    }

                    // 合并数据
                    rangeDriftData.MeasureDataList = beforeData.Concat(afterData).ToList();
                    measureDataList.Add(rangeDriftData);
                }
            }

            return measureDataList;
        }

        /// <summary>
        /// 获取选中的标准量程和辅助量程曲线数据
        /// </summary>
        /// <returns>标准量程和辅助量程的曲线数据</returns>
        private (ReportCurveData standardRangeCurveData, ReportCurveData auxiliaryRangeCurveData) GetSelectedRangeCurveData()
        {
            // 获取所有已勾选的行并转换为CurveData
            var selectedCurves = dgvCurveData.GetSelectedData<CurveData>();

            // 检查是否包含了标准量程和辅助量程的数据
            var standardRangeCurve = selectedCurves.FirstOrDefault(c =>
                Math.Abs(c.Range - _deviceConfig.StandardRange) < 0.0001);
            var auxiliaryRangeCurve = selectedCurves.FirstOrDefault(c =>
                Math.Abs(c.Range - _deviceConfig.AuxiliaryRange) < 0.0001);

            if(standardRangeCurve == null)
            {
                throw new Exception($"请选择一条标准量程[{_deviceConfig.StandardRange})]的曲线数据！");
            }

            if(auxiliaryRangeCurve == null)
            {
                throw new Exception($"请选择一条辅助量程[{_deviceConfig.AuxiliaryRange}]的曲线数据！");
            }

            // 检查是否只选择了两条数据
            if(selectedCurves.Count != 2)
            {
                throw new Exception("请只选择两条曲线数据，分别对应标准量程和辅助量程！");
            }

            // 转换为ReportCurveData类型
            var standardRangeCurveData = new ReportCurveData
            {
                Time = standardRangeCurve.Time,
                Range = standardRangeCurve.Range,
                K = standardRangeCurve.K,
                B = standardRangeCurve.B,
                A2 = standardRangeCurve.A2
            };

            var auxiliaryRangeCurveData = new ReportCurveData
            {
                Time = auxiliaryRangeCurve.Time,
                Range = auxiliaryRangeCurve.Range,
                K = auxiliaryRangeCurve.K,
                B = auxiliaryRangeCurve.B,
                A2 = auxiliaryRangeCurve.A2
            };

            return (standardRangeCurveData, auxiliaryRangeCurveData);
        }

        /// <summary>
        /// 获取选中的标准量程和辅助量程校准数据
        /// </summary>
        /// <returns>标准量程和辅助量程的校准数据</returns>
        private (ReportCalibrationData standardRangeCalibration, ReportCalibrationData auxiliaryRangeCalibration) GetSelectedCalibrationData()
        {
            // 获取所有已勾选的行并转换为ReportTmpCalibrationData
            List<ReportTmpCalibrationData> selectedCalibrations = dgvCalibrationData.GetSelectedRows()
                .Select(r =>
                {
                    var boundItem = r.DataBoundItem;
                    if(_reportData.DevInfo.IsIMNDevice)
                    {
                        var data = (ImnCalibrationData)boundItem;
                        return new ReportTmpCalibrationData() { Range = data.Range, Value = data.Value, Time = data.Time, Type = data.Type };
                    }
                    else if(_reportData.DevInfo.IsTNDevice)
                    {
                        var data = (TnCalibrationData)boundItem;
                        return new ReportTmpCalibrationData() { Range = data.Range, Value = data.Value, Time = data.Time, Type = data.Type };
                    }
                    else
                    {
                        var data = (CalibrationData)boundItem;
                        return new ReportTmpCalibrationData() { Range = data.Range, Value = data.Value, Time = data.Time, Type = data.Type };
                    }
                })
                .ToList();

            if(selectedCalibrations.Count == 0)
            {
                throw new Exception("未选择任何校准数据！");
            }

            // 封装内部方法，校验指定量程数据
            void ValidateRangeCalibrationData(double range, string rangeName)
            {
                var types = new[] { "标1", "标2" };
                if(_reportData.DevInfo.IsTNDevice)
                {
                    types = new[] { "标1", "标2", "标M" };
                }

                foreach(var type in types)
                {
                    var typeData = selectedCalibrations.Where(c =>
                        Math.Abs(c.Range - range) < 0.0001 && c.Type == type).ToList();

                    if(typeData.Count == 0)
                    {
                        throw new Exception($"请选择一条{rangeName}[{range}]的{type}数据！");
                    }
                    if(typeData.Count > 1)
                    {
                        throw new Exception($"{rangeName}[{range}]的{type}数据选择了{typeData.Count}条，只能选择一条！");
                    }
                }
            }

            // 校验标准量程和辅助量程的数据
            ValidateRangeCalibrationData(_deviceConfig.StandardRange, "标准量程");
            ValidateRangeCalibrationData(_deviceConfig.AuxiliaryRange, "辅助量程");

            // 获取标准量程数据
            var standardRangeCalibration = new ReportCalibrationData
            {
                Range = _deviceConfig.StandardRange,
                Standard1Value = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.StandardRange) < 0.0001 && c.Type == "标1").Value,
                Standard1Time = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.StandardRange) < 0.0001 && c.Type == "标1").Time,
                Standard2Value = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.StandardRange) < 0.0001 && c.Type == "标2").Value,
                Standard2Time = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.StandardRange) < 0.0001 && c.Type == "标2").Time,
            };

            // 获取辅助量程数据
            var auxiliaryRangeCalibration = new ReportCalibrationData
            {
                Range = _deviceConfig.AuxiliaryRange,
                Standard1Value = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.AuxiliaryRange) < 0.0001 && c.Type == "标1").Value,
                Standard1Time = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.AuxiliaryRange) < 0.0001 && c.Type == "标1").Time,
                Standard2Value = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.AuxiliaryRange) < 0.0001 && c.Type == "标2").Value,
                Standard2Time = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.AuxiliaryRange) < 0.0001 && c.Type == "标2").Time,
            };

            // 如果是总氮设备，添加标M数据
            if(_reportData.DevInfo.IsTNDevice)
            {
                standardRangeCalibration.StandardMValue = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.StandardRange) < 0.0001 && c.Type == "标M").Value;
                standardRangeCalibration.StandardMTime = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.StandardRange) < 0.0001 && c.Type == "标M").Time;

                auxiliaryRangeCalibration.StandardMValue = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.AuxiliaryRange) < 0.0001 && c.Type == "标M").Value;
                auxiliaryRangeCalibration.StandardMTime = selectedCalibrations.First(c =>
                    Math.Abs(c.Range - _deviceConfig.AuxiliaryRange) < 0.0001 && c.Type == "标M").Time;
            }

            return (standardRangeCalibration, auxiliaryRangeCalibration);
        }

        #endregion
    }
}