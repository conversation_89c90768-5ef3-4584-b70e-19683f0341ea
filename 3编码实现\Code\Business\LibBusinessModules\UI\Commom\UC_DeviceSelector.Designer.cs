namespace LibBusinessModules.UI.Commom
{
    partial class UC_DeviceSelector
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.cmbDeviceList = new Sunny.UI.UIComboDataGridView();
            this.SuspendLayout();
            // 
            // cmbDeviceList
            // 
            this.cmbDeviceList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.cmbDeviceList.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbDeviceList.FillColor = System.Drawing.Color.White;
            this.cmbDeviceList.Font = new System.Drawing.Font("宋体", 12F);
            this.cmbDeviceList.Location = new System.Drawing.Point(0, 0);
            this.cmbDeviceList.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbDeviceList.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbDeviceList.Name = "cmbDeviceList";
            this.cmbDeviceList.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbDeviceList.Size = new System.Drawing.Size(345, 29);
            this.cmbDeviceList.SymbolSize = 24;
            this.cmbDeviceList.TabIndex = 76;
            this.cmbDeviceList.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbDeviceList.Watermark = "";
            this.cmbDeviceList.ValueChanged += new Sunny.UI.UIComboDataGridView.OnValueChanged(this.cmbDeviceList_ValueChanged);
            // 
            // UC_DeviceSelector
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.cmbDeviceList);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_DeviceSelector";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(345, 29);
            this.Load += new System.EventHandler(this.UC_DeviceSelector_Load);
            this.Enter += new System.EventHandler(this.UC_DeviceSelector_Enter);
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIComboDataGridView cmbDeviceList;
    }
}
