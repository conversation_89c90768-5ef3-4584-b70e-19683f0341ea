﻿using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 加标回收数据查询
    /// </summary>
    public partial class UC_AddCheckDataQuery : UC_DataQueryBase2
    {
        public UC_AddCheckDataQuery()
        {
            InitializeComponent();
            QueryDataName = "加标回收数据";
        }

        protected override void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("SNCode", "设备序列号");
            dgvRecords.Columns.Add("Time", "数据时间");
            dgvRecords.Columns.Add("Value1", "加标前测量值");
            dgvRecords.Columns.Add("Time1", "加标前采样时间");
            dgvRecords.Columns.Add("Value2", "加标后测量值");
            dgvRecords.Columns.Add("Time2", "加标后采样时间");
            dgvRecords.Columns.Add("AddVolume", "加标体积");
            dgvRecords.Columns.Add("Standard", "标准值");
            dgvRecords.Columns.Add("Rate", "回收率");
            dgvRecords.Columns.Add("Judge", "回收判定");

        }

        protected override int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<AddCheckData>()
                           .Where(data => data.Time >= StartTime && data.Time <= EndTime);

            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        protected override void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<AddCheckData>()
                               .Where(data => data.Time >= StartTime && data.Time <= EndTime);
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }
                var dataList = query.ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");

                int index = 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["SNCode"].Value = data.SNCode;
                    dr.Cells["Time"].Value = data.Time.ToDisplayFormat();
                    dr.Cells["Value1"].Value = data.Value1.ToString("F4");
                    dr.Cells["Time1"].Value = data.Time1.ToDisplayFormat();
                    dr.Cells["Value2"].Value = data.Value2.ToString("F4");
                    dr.Cells["Time2"].Value = data.Time2.ToDisplayFormat();
                    dr.Cells["AddVolume"].Value = data.AddVolume.ToString("F4");
                    dr.Cells["Standard"].Value = data.Standard.ToString("F4");
                    dr.Cells["Rate"].Value = data.Rate.ToString("F4");
                    dr.Cells["Judge"].Value = data.Judge;

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index++}/{dataList.Count}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    // 线程切换，防止最终进度界面无法关闭
                    //Thread.Sleep(1);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }
    }
}