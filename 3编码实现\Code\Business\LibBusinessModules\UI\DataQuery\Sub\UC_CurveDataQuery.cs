﻿using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 曲线数据查询
    /// </summary>
    public partial class UC_CurveDataQuery : UC_DataQueryBase2
    {
        public UC_CurveDataQuery()
        {
            InitializeComponent();
            QueryDataName = "曲线数据";
        }

        protected override void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("SNCode", "设备序列号");
            dgvRecords.Columns.Add("Time", "数据时间");
            dgvRecords.Columns.Add("Range", "量程");
            dgvRecords.Columns.Add("K", "斜率");
            dgvRecords.Columns.Add("B", "截距");
            dgvRecords.Columns.Add("A2", "二次项");
            dgvRecords.Columns.Add("TimeAdj1", "标1关联校准点时间");
            dgvRecords.Columns.Add("TimeAdj2", "标2关联校准点时间");
            dgvRecords.Columns.Add("TimeAdjM", "中间点关联校准点时间");
        }

        protected override int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<CurveData>()
                           .Where(data => data.Time >= StartTime && data.Time <= EndTime);

            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        protected override void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<CurveData>()
                               .Where(data => data.Time >= StartTime && data.Time <= EndTime);
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }
                var dataList = query.ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");

                int index = 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["SNCode"].Value = data.SNCode;
                    dr.Cells["Time"].Value = data.Time.ToDisplayFormat();
                    dr.Cells["Range"].Value = data.Range;
                    dr.Cells["K"].Value = data.K.ToString("F4");
                    dr.Cells["B"].Value = data.B.ToString("F4");
                    dr.Cells["A2"].Value = data.A2.ToString("F4");
                    dr.Cells["TimeAdj1"].Value = data.TimeAdj1.ToDisplayFormat();
                    dr.Cells["TimeAdj2"].Value = data.TimeAdj2.ToDisplayFormat();
                    dr.Cells["TimeAdjM"].Value = data.TimeAdjM.ToDisplayFormat();

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index++}/{dataList.Count}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    // 线程切换，防止最终进度界面无法关闭
                    //Thread.Sleep(1);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }
    }
}