﻿using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 设备光源信息查询界面
    /// </summary>
    public partial class UC_LightSourceInfoQuery : UC_DataQueryBase2
    {
        public UC_LightSourceInfoQuery()
        {
            InitializeComponent();
            QueryDataName = "设备光源信息";
        }

        protected override void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("SNCode", "设备序列号");
            dgvRecords.Columns.Add("Time", "数据时间");
            dgvRecords.Columns.Add("Current", "信号电流");
            dgvRecords.Columns.Add("MainIndex", "检测信号档位");
            dgvRecords.Columns.Add("MainV1", "检测电压1");
            dgvRecords.Columns.Add("MainV2", "检测电压2");
            dgvRecords.Columns.Add("MainV3", "检测电压3");
            dgvRecords.Columns.Add("MainV4", "检测电压4");
            dgvRecords.Columns.Add("RefIndex", "参比信号档位");
            dgvRecords.Columns.Add("RefV1", "参比电压1");
            dgvRecords.Columns.Add("RefV2", "参比电压2");
            dgvRecords.Columns.Add("RefV3", "参比电压3");
            dgvRecords.Columns.Add("RefV4", "参比电压4");
        }

        protected override int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<LightSourceInfo>()
            .Where(data => data.Time >= StartTime && data.Time <= EndTime);

            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        protected override void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<LightSourceInfo>();
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }
                var dataList = query.ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");

                int index = 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["SNCode"].Value = data.SNCode;
                    dr.Cells["Time"].Value = data.Time.ToDisplayFormat();
                    dr.Cells["Current"].Value = data.Current.ToString("F4");
                    dr.Cells["MainIndex"].Value = data.MainIndex;
                    dr.Cells["MainV1"].Value = data.MainV1.ToString("F4");
                    dr.Cells["MainV2"].Value = data.MainV2.ToString("F4");
                    dr.Cells["MainV3"].Value = data.MainV3.ToString("F4");
                    dr.Cells["MainV4"].Value = data.MainV4.ToString("F4");
                    dr.Cells["RefIndex"].Value = data.RefIndex;
                    dr.Cells["RefV1"].Value = data.RefV1.ToString("F4");
                    dr.Cells["RefV2"].Value = data.RefV2.ToString("F4");
                    dr.Cells["RefV3"].Value = data.RefV3.ToString("F4");
                    dr.Cells["RefV4"].Value = data.RefV4.ToString("F4");

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index++}/{dataList.Count}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    // 线程切换，防止最终进度界面无法关闭
                    //Thread.Sleep(1);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }
    }
}