<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtSnCode.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="txtSnCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>62, 11</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtSnCode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="txtSnCode.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="txtSnCode.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 5, 5, 5</value>
  </data>
  <data name="txtSnCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>149, 29</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtSnCode.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="&gt;&gt;txtSnCode.Name" xml:space="preserve">
    <value>txtSnCode</value>
  </data>
  <data name="&gt;&gt;txtSnCode.Type" xml:space="preserve">
    <value>Sunny.UI.UITextBox, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;txtSnCode.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;txtSnCode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="uiLabel1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="uiLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="uiLabel1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="uiLabel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 17</value>
  </data>
  <data name="uiLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 16</value>
  </data>
  <data name="uiLabel1.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="uiLabel1.Text" xml:space="preserve">
    <value>设备SN</value>
  </data>
  <data name="uiLabel1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;uiLabel1.Name" xml:space="preserve">
    <value>uiLabel1</value>
  </data>
  <data name="&gt;&gt;uiLabel1.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel1.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;uiLabel1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnEnd.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnEnd.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="btnEnd.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 9</value>
  </data>
  <data name="btnEnd.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnEnd.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 32</value>
  </data>
  <data name="btnEnd.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="btnEnd.Text" xml:space="preserve">
    <value>最后一页</value>
  </data>
  <data name="&gt;&gt;btnEnd.Name" xml:space="preserve">
    <value>btnEnd</value>
  </data>
  <data name="&gt;&gt;btnEnd.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnEnd.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;btnEnd.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnFirst.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnFirst.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="btnFirst.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 9</value>
  </data>
  <data name="btnFirst.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnFirst.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 32</value>
  </data>
  <data name="btnFirst.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="btnFirst.Text" xml:space="preserve">
    <value>第一页</value>
  </data>
  <data name="&gt;&gt;btnFirst.Name" xml:space="preserve">
    <value>btnFirst</value>
  </data>
  <data name="&gt;&gt;btnFirst.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnFirst.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;btnFirst.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnNext.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnNext.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>396, 9</value>
  </data>
  <data name="btnNext.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 32</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>下一页</value>
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnPrev.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnPrev.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>307, 9</value>
  </data>
  <data name="btnPrev.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnPrev.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 32</value>
  </data>
  <data name="btnPrev.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="btnPrev.Text" xml:space="preserve">
    <value>上一页</value>
  </data>
  <data name="&gt;&gt;btnPrev.Name" xml:space="preserve">
    <value>btnPrev</value>
  </data>
  <data name="&gt;&gt;btnPrev.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnPrev.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;btnPrev.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtRecordCount.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="txtRecordCount.Location" type="System.Drawing.Point, System.Drawing">
    <value>660, 11</value>
  </data>
  <data name="txtRecordCount.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="txtRecordCount.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="txtRecordCount.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 5, 5, 5</value>
  </data>
  <data name="txtRecordCount.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 29</value>
  </data>
  <data name="txtRecordCount.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="txtRecordCount.Text" xml:space="preserve">
    <value>600</value>
  </data>
  <data name="&gt;&gt;txtRecordCount.Name" xml:space="preserve">
    <value>txtRecordCount</value>
  </data>
  <data name="&gt;&gt;txtRecordCount.Type" xml:space="preserve">
    <value>Sunny.UI.UITextBox, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;txtRecordCount.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;txtRecordCount.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lblPage.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblPage.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="lblPage.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblPage.Location" type="System.Drawing.Point, System.Drawing">
    <value>806, 17</value>
  </data>
  <data name="lblPage.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 16</value>
  </data>
  <data name="lblPage.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="lblPage.Text" xml:space="preserve">
    <value>?/?</value>
  </data>
  <data name="lblPage.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;lblPage.Name" xml:space="preserve">
    <value>lblPage</value>
  </data>
  <data name="&gt;&gt;lblPage.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;lblPage.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;lblPage.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="uiLabel7.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="uiLabel7.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="uiLabel7.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="uiLabel7.Location" type="System.Drawing.Point, System.Drawing">
    <value>759, 17</value>
  </data>
  <data name="uiLabel7.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 16</value>
  </data>
  <data name="uiLabel7.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="uiLabel7.Text" xml:space="preserve">
    <value>页码:</value>
  </data>
  <data name="uiLabel7.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;uiLabel7.Name" xml:space="preserve">
    <value>uiLabel7</value>
  </data>
  <data name="&gt;&gt;uiLabel7.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel7.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;uiLabel7.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="uiLabel6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="uiLabel6.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="uiLabel6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="uiLabel6.Location" type="System.Drawing.Point, System.Drawing">
    <value>571, 17</value>
  </data>
  <data name="uiLabel6.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 16</value>
  </data>
  <data name="uiLabel6.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="uiLabel6.Text" xml:space="preserve">
    <value>每页记录数</value>
  </data>
  <data name="uiLabel6.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;uiLabel6.Name" xml:space="preserve">
    <value>uiLabel6</value>
  </data>
  <data name="&gt;&gt;uiLabel6.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel6.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;uiLabel6.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="btnExcelExport.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnExcelExport.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="btnExcelExport.Location" type="System.Drawing.Point, System.Drawing">
    <value>1101, 9</value>
  </data>
  <data name="btnExcelExport.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnExcelExport.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 32</value>
  </data>
  <data name="btnExcelExport.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="btnExcelExport.Text" xml:space="preserve">
    <value>Excel导出</value>
  </data>
  <data name="&gt;&gt;btnExcelExport.Name" xml:space="preserve">
    <value>btnExcelExport</value>
  </data>
  <data name="&gt;&gt;btnExcelExport.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnExcelExport.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;btnExcelExport.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="btnStartQuery.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnStartQuery.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="btnStartQuery.Location" type="System.Drawing.Point, System.Drawing">
    <value>998, 9</value>
  </data>
  <data name="btnStartQuery.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnStartQuery.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 32</value>
  </data>
  <data name="btnStartQuery.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="btnStartQuery.Text" xml:space="preserve">
    <value>查询</value>
  </data>
  <data name="&gt;&gt;btnStartQuery.Name" xml:space="preserve">
    <value>btnStartQuery</value>
  </data>
  <data name="&gt;&gt;btnStartQuery.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnStartQuery.Parent" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;btnStartQuery.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="pnlTitle.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="pnlTitle.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="pnlTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="pnlTitle.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="pnlTitle.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="pnlTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>1200, 48</value>
  </data>
  <data name="pnlTitle.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="pnlTitle.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlTitle.Name" xml:space="preserve">
    <value>pnlTitle</value>
  </data>
  <data name="&gt;&gt;pnlTitle.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;pnlTitle.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlTitle.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="saveFileDialog.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="saveFileDialog.Filter" xml:space="preserve">
    <value>excel文件 | *.xls;*.xlsx</value>
  </data>
  <data name="saveFileDialog.Title" xml:space="preserve">
    <value>数据导出</value>
  </data>
  <data name="dgvRecords.ColumnHeadersHeight" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="dgvRecords.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="dgvRecords.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="dgvRecords.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 48</value>
  </data>
  <data name="dgvRecords.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="dgvRecords.Size" type="System.Drawing.Size, System.Drawing">
    <value>1200, 552</value>
  </data>
  <data name="dgvRecords.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;dgvRecords.Name" xml:space="preserve">
    <value>dgvRecords</value>
  </data>
  <data name="&gt;&gt;dgvRecords.Type" xml:space="preserve">
    <value>Sunny.UI.UIDataGridView, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;dgvRecords.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dgvRecords.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>1200, 600</value>
  </data>
  <data name="&gt;&gt;saveFileDialog.Name" xml:space="preserve">
    <value>saveFileDialog</value>
  </data>
  <data name="&gt;&gt;saveFileDialog.Type" xml:space="preserve">
    <value>System.Windows.Forms.SaveFileDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>UC_DeviceInfoQuery</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>