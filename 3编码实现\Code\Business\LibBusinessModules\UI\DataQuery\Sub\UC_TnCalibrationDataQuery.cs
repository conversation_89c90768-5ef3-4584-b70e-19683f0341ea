﻿using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 氙灯类测量数据查询
    /// </summary>
    public partial class UC_TnCalibrationDataQuery : UC_DataQueryBase2
    {
        public UC_TnCalibrationDataQuery()
        {
            InitializeComponent();
            QueryDataName = "氙灯类测量数据";
        }

        protected override void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("SNCode", "设备序列号");
            dgvRecords.Columns.Add("Time", "数据时间");
            dgvRecords.Columns.Add("Value", "测量值");
            dgvRecords.Columns.Add("Range", "量程");
            dgvRecords.Columns.Add("Flag", "数据标识");
            dgvRecords.Columns.Add("Type1", "类型1");
            dgvRecords.Columns.Add("Type", "类型2");
            dgvRecords.Columns.Add("Abs", "吸光度");
            dgvRecords.Columns.Add("Abs1", "吸光度220");
            dgvRecords.Columns.Add("Abs2", "吸光度275");
            dgvRecords.Columns.Add("Signal2201", "V1_220");
            dgvRecords.Columns.Add("Signal2751", "V1_275");
            dgvRecords.Columns.Add("Signal2202", "V2_220");
            dgvRecords.Columns.Add("Signal2752", "V2_275");
        }

        protected override int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<TnCalibrationData>()
                           .Where(data => data.Time >= StartTime && data.Time <= EndTime);

            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        protected override void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<TnCalibrationData>()
                               .Where(data => data.Time >= StartTime && data.Time <= EndTime);
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }
                var dataList = query.ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");
                //ExtensionsMethod
                int index = 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["SNCode"].Value = data.SNCode;
                    dr.Cells["Time"].Value = data.Time.ToDisplayFormat();
                    dr.Cells["Value"].Value = data.Value.ToString("F4");
                    dr.Cells["Range"].Value = data.Range;
                    dr.Cells["Flag"].Value = data.Flag;
                    dr.Cells["Type1"].Value = data.Type1;
                    dr.Cells["Type"].Value = data.Type;
                    dr.Cells["Abs"].Value = data.Abs;
                    dr.Cells["Abs1"].Value = data.Abs1;
                    dr.Cells["Abs2"].Value = data.Abs2;
                    dr.Cells["Signal2201"].Value = data.Signal2201;
                    dr.Cells["Signal2751"].Value = data.Signal2751;
                    dr.Cells["Signal2202"].Value = data.Signal2202;
                    dr.Cells["Signal2752"].Value = data.Signal2752;

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index++}/{dataList.Count}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    // 线程切换，防止最终进度界面无法关闭
                    //Thread.Sleep(1);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }
    }
}