﻿namespace LibBusinessModules.UI.DataQuery
{
    partial class UC_AllDataQuery
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tabMain = new Sunny.UI.UITabControlMenu();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.uC_DeviceInfoQuery = new LibBusinessModules.UI.DataQuery.UC_DeviceInfoQuery();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.uC_LightSourceInfoQuery = new LibBusinessModules.UI.DataQuery.UC_LightSourceInfoQuery();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.uC_CurveDataQuery = new LibBusinessModules.UI.DataQuery.UC_CurveDataQuery();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.uC_MeasureDataQuery = new LibBusinessModules.UI.DataQuery.UC_MeasureDataQuery();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.uC_CalibrationDataQuery = new LibBusinessModules.UI.DataQuery.UC_CalibrationDataQuery();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.uC_ImnMeasureDataQuery = new LibBusinessModules.UI.DataQuery.UC_ImnMeasureDataQuery();
            this.tabPage7 = new System.Windows.Forms.TabPage();
            this.uC_ImnCalibrationDataQuery = new LibBusinessModules.UI.DataQuery.UC_ImnCalibrationDataQuery();
            this.tabPage8 = new System.Windows.Forms.TabPage();
            this.uC_TnMeasureDataQuery = new LibBusinessModules.UI.DataQuery.UC_TnMeasureDataQuery();
            this.tabPage9 = new System.Windows.Forms.TabPage();
            this.uC_TnCalibrationDataQuery = new LibBusinessModules.UI.DataQuery.UC_TnCalibrationDataQuery();
            this.tabPage10 = new System.Windows.Forms.TabPage();
            this.uC_LogQuery = new LibBusinessModules.UI.DataQuery.UC_LogQuery();
            this.tabPage11 = new System.Windows.Forms.TabPage();
            this.uC_AlarmQuery = new LibBusinessModules.UI.DataQuery.UC_AlarmQuery();
            this.tabPage12 = new System.Windows.Forms.TabPage();
            this.uC_ZeroCheckDataQuery = new LibBusinessModules.UI.DataQuery.UC_ZeroCheckDataQuery();
            this.tabPage13 = new System.Windows.Forms.TabPage();
            this.uC_SpanCheckDataQuery = new LibBusinessModules.UI.DataQuery.UC_SpanCheckDataQuery();
            this.tabPage14 = new System.Windows.Forms.TabPage();
            this.uC_AddCheckDataQuery = new LibBusinessModules.UI.DataQuery.UC_AddCheckDataQuery();
            this.tabPage15 = new System.Windows.Forms.TabPage();
            this.uC_DoubleCheckDataQuery = new LibBusinessModules.UI.DataQuery.UC_DoubleCheckDataQuery();
            this.pnlTitle = new Sunny.UI.UIPanel();
            this.txtSnCode = new Sunny.UI.UITextBox();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.dtpEndTime = new Sunny.UI.UIDatetimePicker();
            this.dtpStartTime = new Sunny.UI.UIDatetimePicker();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.tabMain.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.tabPage6.SuspendLayout();
            this.tabPage7.SuspendLayout();
            this.tabPage8.SuspendLayout();
            this.tabPage9.SuspendLayout();
            this.tabPage10.SuspendLayout();
            this.tabPage11.SuspendLayout();
            this.tabPage12.SuspendLayout();
            this.tabPage13.SuspendLayout();
            this.tabPage14.SuspendLayout();
            this.tabPage15.SuspendLayout();
            this.pnlTitle.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabMain
            // 
            this.tabMain.Alignment = System.Windows.Forms.TabAlignment.Left;
            this.tabMain.Controls.Add(this.tabPage1);
            this.tabMain.Controls.Add(this.tabPage2);
            this.tabMain.Controls.Add(this.tabPage3);
            this.tabMain.Controls.Add(this.tabPage4);
            this.tabMain.Controls.Add(this.tabPage5);
            this.tabMain.Controls.Add(this.tabPage6);
            this.tabMain.Controls.Add(this.tabPage7);
            this.tabMain.Controls.Add(this.tabPage8);
            this.tabMain.Controls.Add(this.tabPage9);
            this.tabMain.Controls.Add(this.tabPage10);
            this.tabMain.Controls.Add(this.tabPage11);
            this.tabMain.Controls.Add(this.tabPage12);
            this.tabMain.Controls.Add(this.tabPage13);
            this.tabMain.Controls.Add(this.tabPage14);
            this.tabMain.Controls.Add(this.tabPage15);
            this.tabMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabMain.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.tabMain.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabMain.Location = new System.Drawing.Point(0, 42);
            this.tabMain.MenuStyle = Sunny.UI.UIMenuStyle.Custom;
            this.tabMain.Multiline = true;
            this.tabMain.Name = "tabMain";
            this.tabMain.SelectedIndex = 0;
            this.tabMain.Size = new System.Drawing.Size(1192, 669);
            this.tabMain.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabMain.TabIndex = 1;
            this.tabMain.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.uC_DeviceInfoQuery);
            this.tabPage1.Location = new System.Drawing.Point(201, 0);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(991, 669);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "设备基础信息";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // uC_DeviceInfoQuery
            // 
            this.uC_DeviceInfoQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_DeviceInfoQuery.Font = new System.Drawing.Font("宋体", 12F);
            this.uC_DeviceInfoQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_DeviceInfoQuery.Name = "uC_DeviceInfoQuery";
            this.uC_DeviceInfoQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_DeviceInfoQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_DeviceInfoQuery.TabIndex = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.uC_LightSourceInfoQuery);
            this.tabPage2.Location = new System.Drawing.Point(201, 0);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Size = new System.Drawing.Size(991, 669);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "设备光源信息";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // uC_LightSourceInfoQuery
            // 
            this.uC_LightSourceInfoQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_LightSourceInfoQuery.Font = new System.Drawing.Font("宋体", 12F);
            this.uC_LightSourceInfoQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_LightSourceInfoQuery.Name = "uC_LightSourceInfoQuery";
            this.uC_LightSourceInfoQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_LightSourceInfoQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_LightSourceInfoQuery.TabIndex = 0;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.uC_CurveDataQuery);
            this.tabPage3.Location = new System.Drawing.Point(201, 0);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(991, 669);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "曲线数据";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // uC_CurveDataQuery
            // 
            this.uC_CurveDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_CurveDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_CurveDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_CurveDataQuery.Name = "uC_CurveDataQuery";
            this.uC_CurveDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_CurveDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_CurveDataQuery.TabIndex = 0;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.uC_MeasureDataQuery);
            this.tabPage4.Location = new System.Drawing.Point(201, 0);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(991, 669);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "常规测量数据";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // uC_MeasureDataQuery
            // 
            this.uC_MeasureDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_MeasureDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_MeasureDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_MeasureDataQuery.Name = "uC_MeasureDataQuery";
            this.uC_MeasureDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_MeasureDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_MeasureDataQuery.TabIndex = 2;
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.uC_CalibrationDataQuery);
            this.tabPage5.Location = new System.Drawing.Point(201, 0);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Size = new System.Drawing.Size(991, 669);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "常规校准数据";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // uC_CalibrationDataQuery
            // 
            this.uC_CalibrationDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_CalibrationDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_CalibrationDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_CalibrationDataQuery.Name = "uC_CalibrationDataQuery";
            this.uC_CalibrationDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_CalibrationDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_CalibrationDataQuery.TabIndex = 0;
            // 
            // tabPage6
            // 
            this.tabPage6.Controls.Add(this.uC_ImnMeasureDataQuery);
            this.tabPage6.Location = new System.Drawing.Point(201, 0);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Size = new System.Drawing.Size(991, 669);
            this.tabPage6.TabIndex = 5;
            this.tabPage6.Text = "滴定类测量数据";
            this.tabPage6.UseVisualStyleBackColor = true;
            // 
            // uC_ImnMeasureDataQuery
            // 
            this.uC_ImnMeasureDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_ImnMeasureDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_ImnMeasureDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_ImnMeasureDataQuery.Name = "uC_ImnMeasureDataQuery";
            this.uC_ImnMeasureDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_ImnMeasureDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_ImnMeasureDataQuery.TabIndex = 0;
            // 
            // tabPage7
            // 
            this.tabPage7.Controls.Add(this.uC_ImnCalibrationDataQuery);
            this.tabPage7.Location = new System.Drawing.Point(201, 0);
            this.tabPage7.Name = "tabPage7";
            this.tabPage7.Size = new System.Drawing.Size(991, 669);
            this.tabPage7.TabIndex = 6;
            this.tabPage7.Text = "滴定类校准数据";
            this.tabPage7.UseVisualStyleBackColor = true;
            // 
            // uC_ImnCalibrationDataQuery
            // 
            this.uC_ImnCalibrationDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_ImnCalibrationDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_ImnCalibrationDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_ImnCalibrationDataQuery.Name = "uC_ImnCalibrationDataQuery";
            this.uC_ImnCalibrationDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_ImnCalibrationDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_ImnCalibrationDataQuery.TabIndex = 0;
            // 
            // tabPage8
            // 
            this.tabPage8.Controls.Add(this.uC_TnMeasureDataQuery);
            this.tabPage8.Location = new System.Drawing.Point(201, 0);
            this.tabPage8.Name = "tabPage8";
            this.tabPage8.Size = new System.Drawing.Size(991, 669);
            this.tabPage8.TabIndex = 7;
            this.tabPage8.Text = "氙灯类测量数据";
            this.tabPage8.UseVisualStyleBackColor = true;
            // 
            // uC_TnMeasureDataQuery
            // 
            this.uC_TnMeasureDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_TnMeasureDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_TnMeasureDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_TnMeasureDataQuery.Name = "uC_TnMeasureDataQuery";
            this.uC_TnMeasureDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_TnMeasureDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_TnMeasureDataQuery.TabIndex = 0;
            // 
            // tabPage9
            // 
            this.tabPage9.Controls.Add(this.uC_TnCalibrationDataQuery);
            this.tabPage9.Location = new System.Drawing.Point(201, 0);
            this.tabPage9.Name = "tabPage9";
            this.tabPage9.Size = new System.Drawing.Size(991, 669);
            this.tabPage9.TabIndex = 8;
            this.tabPage9.Text = "氙灯类校准数据";
            this.tabPage9.UseVisualStyleBackColor = true;
            // 
            // uC_TnCalibrationDataQuery
            // 
            this.uC_TnCalibrationDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_TnCalibrationDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_TnCalibrationDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_TnCalibrationDataQuery.Name = "uC_TnCalibrationDataQuery";
            this.uC_TnCalibrationDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_TnCalibrationDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_TnCalibrationDataQuery.TabIndex = 0;
            // 
            // tabPage10
            // 
            this.tabPage10.Controls.Add(this.uC_LogQuery);
            this.tabPage10.Location = new System.Drawing.Point(201, 0);
            this.tabPage10.Name = "tabPage10";
            this.tabPage10.Size = new System.Drawing.Size(991, 669);
            this.tabPage10.TabIndex = 9;
            this.tabPage10.Text = "设备操作日志";
            this.tabPage10.UseVisualStyleBackColor = true;
            // 
            // uC_LogQuery
            // 
            this.uC_LogQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_LogQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_LogQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_LogQuery.Name = "uC_LogQuery";
            this.uC_LogQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_LogQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_LogQuery.TabIndex = 0;
            // 
            // tabPage11
            // 
            this.tabPage11.Controls.Add(this.uC_AlarmQuery);
            this.tabPage11.Location = new System.Drawing.Point(201, 0);
            this.tabPage11.Name = "tabPage11";
            this.tabPage11.Size = new System.Drawing.Size(991, 669);
            this.tabPage11.TabIndex = 10;
            this.tabPage11.Text = "设备报警记录";
            this.tabPage11.UseVisualStyleBackColor = true;
            // 
            // uC_AlarmQuery
            // 
            this.uC_AlarmQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_AlarmQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_AlarmQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_AlarmQuery.Name = "uC_AlarmQuery";
            this.uC_AlarmQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_AlarmQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_AlarmQuery.TabIndex = 0;
            // 
            // tabPage12
            // 
            this.tabPage12.Controls.Add(this.uC_ZeroCheckDataQuery);
            this.tabPage12.Location = new System.Drawing.Point(201, 0);
            this.tabPage12.Name = "tabPage12";
            this.tabPage12.Size = new System.Drawing.Size(991, 669);
            this.tabPage12.TabIndex = 11;
            this.tabPage12.Text = "零点核查数据";
            this.tabPage12.UseVisualStyleBackColor = true;
            // 
            // uC_ZeroCheckDataQuery
            // 
            this.uC_ZeroCheckDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_ZeroCheckDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_ZeroCheckDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_ZeroCheckDataQuery.Name = "uC_ZeroCheckDataQuery";
            this.uC_ZeroCheckDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_ZeroCheckDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_ZeroCheckDataQuery.TabIndex = 0;
            // 
            // tabPage13
            // 
            this.tabPage13.Controls.Add(this.uC_SpanCheckDataQuery);
            this.tabPage13.Location = new System.Drawing.Point(201, 0);
            this.tabPage13.Name = "tabPage13";
            this.tabPage13.Size = new System.Drawing.Size(991, 669);
            this.tabPage13.TabIndex = 12;
            this.tabPage13.Text = "跨度核查数据";
            this.tabPage13.UseVisualStyleBackColor = true;
            // 
            // uC_SpanCheckDataQuery
            // 
            this.uC_SpanCheckDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_SpanCheckDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_SpanCheckDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_SpanCheckDataQuery.Name = "uC_SpanCheckDataQuery";
            this.uC_SpanCheckDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_SpanCheckDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_SpanCheckDataQuery.TabIndex = 0;
            // 
            // tabPage14
            // 
            this.tabPage14.Controls.Add(this.uC_AddCheckDataQuery);
            this.tabPage14.Location = new System.Drawing.Point(201, 0);
            this.tabPage14.Name = "tabPage14";
            this.tabPage14.Size = new System.Drawing.Size(991, 669);
            this.tabPage14.TabIndex = 13;
            this.tabPage14.Text = "加标回收数据";
            this.tabPage14.UseVisualStyleBackColor = true;
            // 
            // uC_AddCheckDataQuery
            // 
            this.uC_AddCheckDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_AddCheckDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_AddCheckDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_AddCheckDataQuery.Name = "uC_AddCheckDataQuery";
            this.uC_AddCheckDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_AddCheckDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_AddCheckDataQuery.TabIndex = 0;
            // 
            // tabPage15
            // 
            this.tabPage15.Controls.Add(this.uC_DoubleCheckDataQuery);
            this.tabPage15.Location = new System.Drawing.Point(201, 0);
            this.tabPage15.Name = "tabPage15";
            this.tabPage15.Size = new System.Drawing.Size(991, 669);
            this.tabPage15.TabIndex = 14;
            this.tabPage15.Text = "平行样测试数据";
            this.tabPage15.UseVisualStyleBackColor = true;
            // 
            // uC_DoubleCheckDataQuery
            // 
            this.uC_DoubleCheckDataQuery.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_DoubleCheckDataQuery.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_DoubleCheckDataQuery.Location = new System.Drawing.Point(0, 0);
            this.uC_DoubleCheckDataQuery.Name = "uC_DoubleCheckDataQuery";
            this.uC_DoubleCheckDataQuery.Padding = new System.Windows.Forms.Padding(1);
            this.uC_DoubleCheckDataQuery.Size = new System.Drawing.Size(991, 669);
            this.uC_DoubleCheckDataQuery.TabIndex = 0;
            // 
            // pnlTitle
            // 
            this.pnlTitle.Controls.Add(this.txtSnCode);
            this.pnlTitle.Controls.Add(this.uiLabel1);
            this.pnlTitle.Controls.Add(this.dtpEndTime);
            this.pnlTitle.Controls.Add(this.dtpStartTime);
            this.pnlTitle.Controls.Add(this.uiLabel2);
            this.pnlTitle.Controls.Add(this.uiLabel3);
            this.pnlTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTitle.FillColor = System.Drawing.Color.White;
            this.pnlTitle.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.pnlTitle.Location = new System.Drawing.Point(0, 0);
            this.pnlTitle.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTitle.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTitle.Name = "pnlTitle";
            this.pnlTitle.Size = new System.Drawing.Size(1192, 42);
            this.pnlTitle.Style = Sunny.UI.UIStyle.Custom;
            this.pnlTitle.TabIndex = 8;
            this.pnlTitle.Text = null;
            this.pnlTitle.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtSnCode
            // 
            this.txtSnCode.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtSnCode.Font = new System.Drawing.Font("宋体", 12F);
            this.txtSnCode.Location = new System.Drawing.Point(842, 7);
            this.txtSnCode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSnCode.MinimumSize = new System.Drawing.Size(1, 1);
            this.txtSnCode.Name = "txtSnCode";
            this.txtSnCode.Padding = new System.Windows.Forms.Padding(5);
            this.txtSnCode.ShowText = false;
            this.txtSnCode.Size = new System.Drawing.Size(186, 29);
            this.txtSnCode.Style = Sunny.UI.UIStyle.Custom;
            this.txtSnCode.TabIndex = 36;
            this.txtSnCode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtSnCode.Watermark = "";
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.uiLabel1.Location = new System.Drawing.Point(778, 13);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(55, 16);
            this.uiLabel1.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel1.TabIndex = 37;
            this.uiLabel1.Text = "设备SN";
            this.uiLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // dtpEndTime
            // 
            this.dtpEndTime.FillColor = System.Drawing.Color.White;
            this.dtpEndTime.Font = new System.Drawing.Font("宋体", 12F);
            this.dtpEndTime.Location = new System.Drawing.Point(573, 7);
            this.dtpEndTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpEndTime.MaxLength = 19;
            this.dtpEndTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpEndTime.Name = "dtpEndTime";
            this.dtpEndTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpEndTime.Size = new System.Drawing.Size(186, 29);
            this.dtpEndTime.Style = Sunny.UI.UIStyle.Custom;
            this.dtpEndTime.SymbolDropDown = 61555;
            this.dtpEndTime.SymbolNormal = 61555;
            this.dtpEndTime.SymbolSize = 24;
            this.dtpEndTime.TabIndex = 23;
            this.dtpEndTime.Text = "2021-01-25 10:00:03";
            this.dtpEndTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpEndTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpEndTime.Watermark = "";
            // 
            // dtpStartTime
            // 
            this.dtpStartTime.FillColor = System.Drawing.Color.White;
            this.dtpStartTime.Font = new System.Drawing.Font("宋体", 12F);
            this.dtpStartTime.Location = new System.Drawing.Point(297, 7);
            this.dtpStartTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpStartTime.MaxLength = 19;
            this.dtpStartTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpStartTime.Size = new System.Drawing.Size(186, 29);
            this.dtpStartTime.Style = Sunny.UI.UIStyle.Custom;
            this.dtpStartTime.SymbolDropDown = 61555;
            this.dtpStartTime.SymbolNormal = 61555;
            this.dtpStartTime.SymbolSize = 24;
            this.dtpStartTime.TabIndex = 22;
            this.dtpStartTime.Text = "2021-01-25 10:00:03";
            this.dtpStartTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpStartTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpStartTime.Watermark = "";
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.uiLabel2.Location = new System.Drawing.Point(502, 13);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(71, 16);
            this.uiLabel2.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel2.TabIndex = 33;
            this.uiLabel2.Text = "结束时间";
            this.uiLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.uiLabel3.Location = new System.Drawing.Point(225, 13);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(71, 16);
            this.uiLabel3.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel3.TabIndex = 32;
            this.uiLabel3.Text = "起始时间";
            this.uiLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // UC_AllDataQuery
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.tabMain);
            this.Controls.Add(this.pnlTitle);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_AllDataQuery";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1192, 711);
            this.Text = "数据查询";
            this.Load += new System.EventHandler(this.UC_AllDataQuery_Load);
            this.tabMain.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.tabPage5.ResumeLayout(false);
            this.tabPage6.ResumeLayout(false);
            this.tabPage7.ResumeLayout(false);
            this.tabPage8.ResumeLayout(false);
            this.tabPage9.ResumeLayout(false);
            this.tabPage10.ResumeLayout(false);
            this.tabPage11.ResumeLayout(false);
            this.tabPage12.ResumeLayout(false);
            this.tabPage13.ResumeLayout(false);
            this.tabPage14.ResumeLayout(false);
            this.tabPage15.ResumeLayout(false);
            this.pnlTitle.ResumeLayout(false);
            this.pnlTitle.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UITabControlMenu tabMain;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.TabPage tabPage7;
        private System.Windows.Forms.TabPage tabPage8;
        private System.Windows.Forms.TabPage tabPage9;
        private System.Windows.Forms.TabPage tabPage10;
        private System.Windows.Forms.TabPage tabPage11;
        private DataQuery.UC_DeviceInfoQuery uC_DeviceInfoQuery;
        private DataQuery.UC_LightSourceInfoQuery uC_LightSourceInfoQuery;
        private DataQuery.UC_CurveDataQuery uC_CurveDataQuery;
        private DataQuery.UC_MeasureDataQuery uC_MeasureDataQuery;
        private DataQuery.UC_CalibrationDataQuery uC_CalibrationDataQuery;
        private DataQuery.UC_ImnMeasureDataQuery uC_ImnMeasureDataQuery;
        private DataQuery.UC_ImnCalibrationDataQuery uC_ImnCalibrationDataQuery;
        private DataQuery.UC_TnMeasureDataQuery uC_TnMeasureDataQuery;
        private DataQuery.UC_TnCalibrationDataQuery uC_TnCalibrationDataQuery;
        private DataQuery.UC_LogQuery uC_LogQuery;
        private DataQuery.UC_AlarmQuery uC_AlarmQuery;
        private System.Windows.Forms.TabPage tabPage12;
        private System.Windows.Forms.TabPage tabPage13;
        private System.Windows.Forms.TabPage tabPage14;
        private System.Windows.Forms.TabPage tabPage15;
        private DataQuery.UC_ZeroCheckDataQuery uC_ZeroCheckDataQuery;
        private DataQuery.UC_SpanCheckDataQuery uC_SpanCheckDataQuery;
        private DataQuery.UC_AddCheckDataQuery uC_AddCheckDataQuery;
        private DataQuery.UC_DoubleCheckDataQuery uC_DoubleCheckDataQuery;
        protected Sunny.UI.UIPanel pnlTitle;
        private Sunny.UI.UITextBox txtSnCode;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIDatetimePicker dtpEndTime;
        private Sunny.UI.UIDatetimePicker dtpStartTime;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel3;
    }
}
