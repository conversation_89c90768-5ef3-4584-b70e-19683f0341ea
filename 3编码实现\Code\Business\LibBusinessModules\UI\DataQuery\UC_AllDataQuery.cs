﻿using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 数据查询
    /// </summary>
    public partial class UC_AllDataQuery : UIUserControl
    {
        public UC_AllDataQuery()
        {
            InitializeComponent();
        }

        private void UC_AllDataQuery_Load(object sender, EventArgs e)
        {
            InitialDateTimePicker();

            // 遍历所有TabPage，设置时间选择器和序列号文本框
            foreach(TabPage tabPage in tabMain.TabPages)
            {
                foreach(var ctrl in tabPage.Controls)
                {
                    if(ctrl is UC_DataQueryBase2 uc)
                    {
                        try
                        {
                            uc.SetTimePicker(dtpStartTime, dtpEndTime);
                            uc.SetSnCodeTextBox(txtSnCode);
                        }
                        catch
                        {
                            // 忽略异常，可能是控件不支持这些方法
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }
    }
}