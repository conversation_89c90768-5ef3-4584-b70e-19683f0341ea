using System;
using System.ComponentModel;

namespace LibBusinessModules.ReportCenter.Config
{
    /// <summary>
    /// 设备趋势统计项数据模型
    /// </summary>
    public class DeviceTrendStatisticsItem
    {
        /// <summary>
        /// 统计时间点
        /// </summary>
        [Description("统计时间点")]
        public DateTime StatisticsTime { get; set; }

        /// <summary>
        /// 时间显示标签（如：2023年1月、第24周、6月5日等）
        /// </summary>
        [Description("时间显示标签")]
        public string TimeLabel { get; set; }

        /// <summary>
        /// 设备类型名称
        /// </summary>
        [Description("设备类型名称")]
        public string DeviceType { get; set; }

        /// <summary>
        /// 设备数量
        /// </summary>
        [Description("设备数量")]
        public int DeviceCount { get; set; }

        /// <summary>
        /// 是否为总数统计
        /// </summary>
        [Description("是否为总数统计")]
        public bool IsTotal { get; set; }
    }

    /// <summary>
    /// 时间周期枚举
    /// </summary>
    public enum TimePeriodType
    {
        /// <summary>
        /// 年统计
        /// </summary>
        [Description("年")]
        Year = 0,

        /// <summary>
        /// 月统计
        /// </summary>
        [Description("月")]
        Month = 1,

        /// <summary>
        /// 周统计
        /// </summary>
        [Description("周")]
        Week = 2,

        /// <summary>
        /// 日统计
        /// </summary>
        [Description("日")]
        Day = 3
    }
}
