{"format": 1, "restore": {"G:\\01-MyCode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\LibBusinessModules.csproj": {}}, "projects": {"G:\\01-MyCode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj", "projectName": "LibBaseModules", "projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj", "packagesPath": "F:\\Nuget\\packages\\", "outputPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://pcnuget.fpi-inc.com/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"MySql.Data": {"target": "Package", "version": "[9.3.0, )"}, "NPOI": {"target": "Package", "version": "[2.7.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SqlSugar": {"target": "Package", "version": "[5.1.4.195, )"}, "SunnyUI": {"target": "Package", "version": "[3.8.2, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "G:\\01-MyCode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\LibBusinessModules.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\LibBusinessModules.csproj", "projectName": "LibBusinessModules", "projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\LibBusinessModules.csproj", "packagesPath": "F:\\Nuget\\packages\\", "outputPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://pcnuget.fpi-inc.com/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"G:\\01-MyCode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj": {"projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"LiveChartsCore": {"target": "Package", "version": "[2.0.0-rc5.4, )"}, "LiveChartsCore.SkiaSharpView.WinForms": {"target": "Package", "version": "[2.0.0-rc5.4, )"}, "MySql.Data": {"target": "Package", "version": "[9.3.0, )"}, "NPOI": {"target": "Package", "version": "[2.7.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SkiaSharp.Views.WindowsForms": {"target": "Package", "version": "[2.88.8, )"}, "SqlSugar": {"target": "Package", "version": "[5.1.4.195, )"}, "SunnyUI": {"target": "Package", "version": "[3.8.2, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.119, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}