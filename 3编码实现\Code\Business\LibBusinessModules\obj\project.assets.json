{"version": 3, "targets": {".NETFramework,Version=v4.8": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "EntityFramework/6.4.4": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "build": {"buildTransitive/EntityFramework.props": {}, "buildTransitive/EntityFramework.targets": {}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp/7.3.0.3": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.3", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "LiveChartsCore/2.0.0-rc5.4": {"type": "package", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc5.4", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WinForms/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc5.4", "SkiaSharp.HarfBuzz": "2.88.9", "SkiaSharp.Views.WindowsForms": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win-x64/native/comerr64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/gssapi64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/k5sprt64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/krb5_64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/krbcc64.dll": {"assetType": "native", "rid": "win-x64"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "OpenTK/3.1.0": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/3.1.0": {"type": "package", "dependencies": {"OpenTK": "3.1.0"}, "compile": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9", "System.Memory": "4.5.5"}, "compile": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.9": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.3", "SkiaSharp": "2.88.9"}, "compile": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp.Views.Desktop.Common/2.88.8": {"type": "package", "dependencies": {"SkiaSharp": "2.88.8", "System.Drawing.Common": "4.7.3"}, "compile": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.Views.WindowsForms/2.88.8": {"type": "package", "dependencies": {"OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "SkiaSharp": "2.88.8", "SkiaSharp.Views.Desktop.Common": "2.88.8"}, "compile": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}}, "SqlSugar/5.1.4.195": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "Stub.System.Data.SQLite.Core.NetFramework/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SQLite/1.0.119": {"type": "package", "dependencies": {"System.Data.SQLite.Core": "[1.0.119]", "System.Data.SQLite.EF6": "[1.0.119]", "System.Data.SQLite.Linq": "[1.0.119]"}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[1.0.119]"}}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "dependencies": {"EntityFramework": "6.4.4"}, "compile": {"lib/net46/System.Data.SQLite.EF6.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.EF6.dll": {}}}, "System.Data.SQLite.Linq/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.Linq.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.Linq.dll": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Drawing.Common/4.7.3": {"type": "package", "frameworkAssemblies": ["System", "System.Drawing", "mscorlib"], "compile": {"ref/net461/System.Drawing.Common.dll": {}}, "runtime": {"lib/net461/System.Drawing.Common.dll": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}, "LibBaseModules/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"MySql.Data": "9.3.0", "NPOI": "2.7.3", "Newtonsoft.Json": "13.0.3", "SqlSugar": "5.1.4.195", "SunnyUI": "3.8.2"}, "compile": {"bin/placeholder/LibBaseModules.dll": {}}, "runtime": {"bin/placeholder/LibBaseModules.dll": {}}}}, ".NETFramework,Version=v4.8/win": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "EntityFramework/6.4.4": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "build": {"buildTransitive/EntityFramework.props": {}, "buildTransitive/EntityFramework.targets": {}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp/7.3.0.3": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.3", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets": {}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "LiveChartsCore/2.0.0-rc5.4": {"type": "package", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc5.4", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WinForms/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc5.4", "SkiaSharp.HarfBuzz": "2.88.9", "SkiaSharp.Views.WindowsForms": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "OpenTK/3.1.0": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/3.1.0": {"type": "package", "dependencies": {"OpenTK": "3.1.0"}, "compile": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9", "System.Memory": "4.5.5"}, "compile": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.9": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.3", "SkiaSharp": "2.88.9"}, "compile": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "SkiaSharp.Views.Desktop.Common/2.88.8": {"type": "package", "dependencies": {"SkiaSharp": "2.88.8", "System.Drawing.Common": "4.7.3"}, "compile": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.Views.WindowsForms/2.88.8": {"type": "package", "dependencies": {"OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "SkiaSharp": "2.88.8", "SkiaSharp.Views.Desktop.Common": "2.88.8"}, "compile": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}}, "SqlSugar/5.1.4.195": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "Stub.System.Data.SQLite.Core.NetFramework/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SQLite/1.0.119": {"type": "package", "dependencies": {"System.Data.SQLite.Core": "[1.0.119]", "System.Data.SQLite.EF6": "[1.0.119]", "System.Data.SQLite.Linq": "[1.0.119]"}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[1.0.119]"}}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "dependencies": {"EntityFramework": "6.4.4"}, "compile": {"lib/net46/System.Data.SQLite.EF6.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.EF6.dll": {}}}, "System.Data.SQLite.Linq/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.Linq.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.Linq.dll": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Drawing.Common/4.7.3": {"type": "package", "frameworkAssemblies": ["System", "System.Drawing", "mscorlib"], "compile": {"ref/net461/System.Drawing.Common.dll": {}}, "runtime": {"lib/net461/System.Drawing.Common.dll": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}, "LibBaseModules/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"MySql.Data": "9.3.0", "NPOI": "2.7.3", "Newtonsoft.Json": "13.0.3", "SqlSugar": "5.1.4.195", "SunnyUI": "3.8.2"}, "compile": {"bin/placeholder/LibBaseModules.dll": {}}, "runtime": {"bin/placeholder/LibBaseModules.dll": {}}}}, ".NETFramework,Version=v4.8/win-arm64": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "EntityFramework/6.4.4": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "build": {"buildTransitive/EntityFramework.props": {}, "buildTransitive/EntityFramework.targets": {}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp/7.3.0.3": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.3", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets": {}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "LiveChartsCore/2.0.0-rc5.4": {"type": "package", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc5.4", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WinForms/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc5.4", "SkiaSharp.HarfBuzz": "2.88.9", "SkiaSharp.Views.WindowsForms": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "OpenTK/3.1.0": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/3.1.0": {"type": "package", "dependencies": {"OpenTK": "3.1.0"}, "compile": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9", "System.Memory": "4.5.5"}, "compile": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.9": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.3", "SkiaSharp": "2.88.9"}, "compile": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-arm64/native/libSkiaSharp.dll": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "SkiaSharp.Views.Desktop.Common/2.88.8": {"type": "package", "dependencies": {"SkiaSharp": "2.88.8", "System.Drawing.Common": "4.7.3"}, "compile": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.Views.WindowsForms/2.88.8": {"type": "package", "dependencies": {"OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "SkiaSharp": "2.88.8", "SkiaSharp.Views.Desktop.Common": "2.88.8"}, "compile": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}}, "SqlSugar/5.1.4.195": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "Stub.System.Data.SQLite.Core.NetFramework/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SQLite/1.0.119": {"type": "package", "dependencies": {"System.Data.SQLite.Core": "[1.0.119]", "System.Data.SQLite.EF6": "[1.0.119]", "System.Data.SQLite.Linq": "[1.0.119]"}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[1.0.119]"}}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "dependencies": {"EntityFramework": "6.4.4"}, "compile": {"lib/net46/System.Data.SQLite.EF6.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.EF6.dll": {}}}, "System.Data.SQLite.Linq/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.Linq.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.Linq.dll": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Drawing.Common/4.7.3": {"type": "package", "frameworkAssemblies": ["System", "System.Drawing", "mscorlib"], "compile": {"ref/net461/System.Drawing.Common.dll": {}}, "runtime": {"lib/net461/System.Drawing.Common.dll": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}, "LibBaseModules/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"MySql.Data": "9.3.0", "NPOI": "2.7.3", "Newtonsoft.Json": "13.0.3", "SqlSugar": "5.1.4.195", "SunnyUI": "3.8.2"}, "compile": {"bin/placeholder/LibBaseModules.dll": {}}, "runtime": {"bin/placeholder/LibBaseModules.dll": {}}}}, ".NETFramework,Version=v4.8/win-x64": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "EntityFramework/6.4.4": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "build": {"buildTransitive/EntityFramework.props": {}, "buildTransitive/EntityFramework.targets": {}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp/7.3.0.3": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.3", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-x64/native/libHarfBuzzSharp.dll": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets": {}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "LiveChartsCore/2.0.0-rc5.4": {"type": "package", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc5.4", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WinForms/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc5.4", "SkiaSharp.HarfBuzz": "2.88.9", "SkiaSharp.Views.WindowsForms": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "native": {"runtimes/win-x64/native/comerr64.dll": {}, "runtimes/win-x64/native/gssapi64.dll": {}, "runtimes/win-x64/native/k5sprt64.dll": {}, "runtimes/win-x64/native/krb5_64.dll": {}, "runtimes/win-x64/native/krbcc64.dll": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "OpenTK/3.1.0": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/3.1.0": {"type": "package", "dependencies": {"OpenTK": "3.1.0"}, "compile": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9", "System.Memory": "4.5.5"}, "compile": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.9": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.3", "SkiaSharp": "2.88.9"}, "compile": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-x64/native/libSkiaSharp.dll": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "SkiaSharp.Views.Desktop.Common/2.88.8": {"type": "package", "dependencies": {"SkiaSharp": "2.88.8", "System.Drawing.Common": "4.7.3"}, "compile": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.Views.WindowsForms/2.88.8": {"type": "package", "dependencies": {"OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "SkiaSharp": "2.88.8", "SkiaSharp.Views.Desktop.Common": "2.88.8"}, "compile": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}}, "SqlSugar/5.1.4.195": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "Stub.System.Data.SQLite.Core.NetFramework/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SQLite/1.0.119": {"type": "package", "dependencies": {"System.Data.SQLite.Core": "[1.0.119]", "System.Data.SQLite.EF6": "[1.0.119]", "System.Data.SQLite.Linq": "[1.0.119]"}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[1.0.119]"}}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "dependencies": {"EntityFramework": "6.4.4"}, "compile": {"lib/net46/System.Data.SQLite.EF6.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.EF6.dll": {}}}, "System.Data.SQLite.Linq/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.Linq.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.Linq.dll": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Drawing.Common/4.7.3": {"type": "package", "frameworkAssemblies": ["System", "System.Drawing", "mscorlib"], "compile": {"ref/net461/System.Drawing.Common.dll": {}}, "runtime": {"lib/net461/System.Drawing.Common.dll": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}, "LibBaseModules/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"MySql.Data": "9.3.0", "NPOI": "2.7.3", "Newtonsoft.Json": "13.0.3", "SqlSugar": "5.1.4.195", "SunnyUI": "3.8.2"}, "compile": {"bin/placeholder/LibBaseModules.dll": {}}, "runtime": {"bin/placeholder/LibBaseModules.dll": {}}}}, ".NETFramework,Version=v4.8/win-x86": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "EntityFramework/6.4.4": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/net45/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/net45/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "build": {"buildTransitive/EntityFramework.props": {}, "buildTransitive/EntityFramework.targets": {}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp/7.3.0.3": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.3", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-x86/native/libHarfBuzzSharp.dll": {}}, "build": {"buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets": {}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "LiveChartsCore/2.0.0-rc5.4": {"type": "package", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc5.4", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WinForms/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc5.4", "SkiaSharp.HarfBuzz": "2.88.9", "SkiaSharp.Views.WindowsForms": "2.88.9"}, "compile": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "OpenTK/3.1.0": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/3.1.0": {"type": "package", "dependencies": {"OpenTK": "3.1.0"}, "compile": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9", "System.Memory": "4.5.5"}, "compile": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.9": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.3", "SkiaSharp": "2.88.9"}, "compile": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "native": {"runtimes/win-x86/native/libSkiaSharp.dll": {}}, "build": {"buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets": {}}}, "SkiaSharp.Views.Desktop.Common/2.88.8": {"type": "package", "dependencies": {"SkiaSharp": "2.88.8", "System.Drawing.Common": "4.7.3"}, "compile": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.Views.WindowsForms/2.88.8": {"type": "package", "dependencies": {"OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "SkiaSharp": "2.88.8", "SkiaSharp.Views.Desktop.Common": "2.88.8"}, "compile": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/SkiaSharp.Views.WindowsForms.dll": {"related": ".pdb;.xml"}}}, "SqlSugar/5.1.4.195": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "Stub.System.Data.SQLite.Core.NetFramework/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SQLite/1.0.119": {"type": "package", "dependencies": {"System.Data.SQLite.Core": "[1.0.119]", "System.Data.SQLite.EF6": "[1.0.119]", "System.Data.SQLite.Linq": "[1.0.119]"}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[1.0.119]"}}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "dependencies": {"EntityFramework": "6.4.4"}, "compile": {"lib/net46/System.Data.SQLite.EF6.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.EF6.dll": {}}}, "System.Data.SQLite.Linq/1.0.119": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.Linq.dll": {}}, "runtime": {"lib/net46/System.Data.SQLite.Linq.dll": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Drawing.Common/4.7.3": {"type": "package", "frameworkAssemblies": ["System", "System.Drawing", "mscorlib"], "compile": {"ref/net461/System.Drawing.Common.dll": {}}, "runtime": {"lib/net461/System.Drawing.Common.dll": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}, "LibBaseModules/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"MySql.Data": "9.3.0", "NPOI": "2.7.3", "Newtonsoft.Json": "13.0.3", "SqlSugar": "5.1.4.195", "SunnyUI": "3.8.2"}, "compile": {"bin/placeholder/LibBaseModules.dll": {}}, "runtime": {"bin/placeholder/LibBaseModules.dll": {}}}}}, "libraries": {"BouncyCastle.Cryptography/2.5.1": {"sha512": "zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg==", "type": "package", "path": "bouncycastle.cryptography/2.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "bouncycastle.cryptography.2.5.1.nupkg.sha512", "bouncycastle.cryptography.nuspec", "lib/net461/BouncyCastle.Cryptography.dll", "lib/net461/BouncyCastle.Cryptography.xml", "lib/net6.0/BouncyCastle.Cryptography.dll", "lib/net6.0/BouncyCastle.Cryptography.xml", "lib/netstandard2.0/BouncyCastle.Cryptography.dll", "lib/netstandard2.0/BouncyCastle.Cryptography.xml", "packageIcon.png"]}, "EntityFramework/6.4.4": {"sha512": "yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "type": "package", "path": "entityframework/6.4.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/EntityFramework.DefaultItems.props", "build/EntityFramework.props", "build/EntityFramework.targets", "build/Microsoft.Data.Entity.Build.Tasks.dll", "build/netcoreapp3.0/EntityFramework.props", "build/netcoreapp3.0/EntityFramework.targets", "buildTransitive/EntityFramework.props", "buildTransitive/EntityFramework.targets", "buildTransitive/netcoreapp3.0/EntityFramework.props", "buildTransitive/netcoreapp3.0/EntityFramework.targets", "content/net40/App.config.install.xdt", "content/net40/App.config.transform", "content/net40/Web.config.install.xdt", "content/net40/Web.config.transform", "entityframework.6.4.4.nupkg.sha512", "entityframework.nuspec", "lib/net40/EntityFramework.SqlServer.dll", "lib/net40/EntityFramework.SqlServer.xml", "lib/net40/EntityFramework.dll", "lib/net40/EntityFramework.xml", "lib/net45/EntityFramework.SqlServer.dll", "lib/net45/EntityFramework.SqlServer.xml", "lib/net45/EntityFramework.dll", "lib/net45/EntityFramework.xml", "lib/netstandard2.1/EntityFramework.SqlServer.dll", "lib/netstandard2.1/EntityFramework.SqlServer.xml", "lib/netstandard2.1/EntityFramework.dll", "lib/netstandard2.1/EntityFramework.xml", "tools/EntityFramework6.PS2.psd1", "tools/EntityFramework6.PS2.psm1", "tools/EntityFramework6.psd1", "tools/EntityFramework6.psm1", "tools/about_EntityFramework6.help.txt", "tools/init.ps1", "tools/install.ps1", "tools/net40/any/ef6.exe", "tools/net40/any/ef6.pdb", "tools/net40/win-x86/ef6.exe", "tools/net40/win-x86/ef6.pdb", "tools/net45/any/ef6.exe", "tools/net45/any/ef6.pdb", "tools/net45/win-x86/ef6.exe", "tools/net45/win-x86/ef6.pdb", "tools/netcoreapp3.0/any/ef6.dll", "tools/netcoreapp3.0/any/ef6.pdb", "tools/netcoreapp3.0/any/ef6.runtimeconfig.json"]}, "Enums.NET/4.0.1": {"sha512": "OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "type": "package", "path": "enums.net/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "enums.net.4.0.1.nupkg.sha512", "enums.net.nuspec", "lib/net45/Enums.NET.dll", "lib/net45/Enums.NET.pdb", "lib/net45/Enums.NET.xml", "lib/netcoreapp3.0/Enums.NET.dll", "lib/netcoreapp3.0/Enums.NET.pdb", "lib/netcoreapp3.0/Enums.NET.xml", "lib/netstandard1.0/Enums.NET.dll", "lib/netstandard1.0/Enums.NET.pdb", "lib/netstandard1.0/Enums.NET.xml", "lib/netstandard1.1/Enums.NET.dll", "lib/netstandard1.1/Enums.NET.pdb", "lib/netstandard1.1/Enums.NET.xml", "lib/netstandard1.3/Enums.NET.dll", "lib/netstandard1.3/Enums.NET.pdb", "lib/netstandard1.3/Enums.NET.xml", "lib/netstandard2.0/Enums.NET.dll", "lib/netstandard2.0/Enums.NET.pdb", "lib/netstandard2.0/Enums.NET.xml", "lib/netstandard2.1/Enums.NET.dll", "lib/netstandard2.1/Enums.NET.pdb", "lib/netstandard2.1/Enums.NET.xml"]}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"sha512": "+woGT1lsBtwkntOpx2EZbdbySv0aWPefE0vrfvclxVdbi4oa2bbtphFPWgMiQe+kRCPICbfFJwp6w1DuR7Ge2Q==", "type": "package", "path": "extendednumerics.bigdecimal/2025.1001.2.129", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "extendednumerics.bigdecimal.2025.1001.2.129.nupkg.sha512", "extendednumerics.bigdecimal.nuspec", "lib/net45/ExtendedNumerics.BigDecimal.dll", "lib/net45/ExtendedNumerics.BigDecimal.xml", "lib/net46/ExtendedNumerics.BigDecimal.dll", "lib/net46/ExtendedNumerics.BigDecimal.xml", "lib/net472/ExtendedNumerics.BigDecimal.dll", "lib/net472/ExtendedNumerics.BigDecimal.xml", "lib/net48/ExtendedNumerics.BigDecimal.dll", "lib/net48/ExtendedNumerics.BigDecimal.xml", "lib/net5.0/ExtendedNumerics.BigDecimal.dll", "lib/net5.0/ExtendedNumerics.BigDecimal.xml", "lib/net6.0/ExtendedNumerics.BigDecimal.dll", "lib/net6.0/ExtendedNumerics.BigDecimal.xml", "lib/net7.0/ExtendedNumerics.BigDecimal.dll", "lib/net7.0/ExtendedNumerics.BigDecimal.xml", "lib/net8.0/ExtendedNumerics.BigDecimal.dll", "lib/net8.0/ExtendedNumerics.BigDecimal.xml", "lib/netcoreapp3.1/ExtendedNumerics.BigDecimal.dll", "lib/netcoreapp3.1/ExtendedNumerics.BigDecimal.xml", "lib/netstandard2.0/ExtendedNumerics.BigDecimal.dll", "lib/netstandard2.0/ExtendedNumerics.BigDecimal.xml", "lib/netstandard2.1/ExtendedNumerics.BigDecimal.dll", "lib/netstandard2.1/ExtendedNumerics.BigDecimal.xml"]}, "Google.Protobuf/3.30.0": {"sha512": "ZnEI4oZWnHvd+Yz5Gcnx5Q5RQIuzptIzd0fmxAN8f81FYHI0USZqMOrPTkrsd/QEzo9vl2b217v9FqFgHfufQw==", "type": "package", "path": "google.protobuf/3.30.0", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.30.0.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "HarfBuzzSharp/7.3.0.3": {"sha512": "Hq+5+gx10coOvuRgB13KBwiWxJq1QeYuhtVLbA01ZCWaugOnolUahF44KvrQTUUHDNk/C7HB6SMaebsZeOdhgg==", "type": "package", "path": "harfbuzzsharp/7.3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "harfbuzzsharp.7.3.0.3.nupkg.sha512", "harfbuzzsharp.nuspec", "lib/monoandroid1.0/HarfBuzzSharp.dll", "lib/monoandroid1.0/HarfBuzzSharp.pdb", "lib/monoandroid1.0/HarfBuzzSharp.xml", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net462/HarfBuzzSharp.xml", "lib/net6.0-android30.0/HarfBuzzSharp.dll", "lib/net6.0-android30.0/HarfBuzzSharp.pdb", "lib/net6.0-android30.0/HarfBuzzSharp.xml", "lib/net6.0-ios13.6/HarfBuzzSharp.dll", "lib/net6.0-ios13.6/HarfBuzzSharp.pdb", "lib/net6.0-ios13.6/HarfBuzzSharp.xml", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.dll", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.pdb", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.xml", "lib/net6.0-macos10.15/HarfBuzzSharp.dll", "lib/net6.0-macos10.15/HarfBuzzSharp.pdb", "lib/net6.0-macos10.15/HarfBuzzSharp.xml", "lib/net6.0-tvos13.4/HarfBuzzSharp.dll", "lib/net6.0-tvos13.4/HarfBuzzSharp.pdb", "lib/net6.0-tvos13.4/HarfBuzzSharp.xml", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.xml", "lib/netcoreapp3.1/HarfBuzzSharp.dll", "lib/netcoreapp3.1/HarfBuzzSharp.pdb", "lib/netcoreapp3.1/HarfBuzzSharp.xml", "lib/netstandard1.3/HarfBuzzSharp.dll", "lib/netstandard1.3/HarfBuzzSharp.pdb", "lib/netstandard1.3/HarfBuzzSharp.xml", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.xml", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.xml", "lib/tizen40/HarfBuzzSharp.dll", "lib/tizen40/HarfBuzzSharp.pdb", "lib/tizen40/HarfBuzzSharp.xml", "lib/uap10.0.10240/HarfBuzzSharp.dll", "lib/uap10.0.10240/HarfBuzzSharp.pdb", "lib/uap10.0.10240/HarfBuzzSharp.xml", "lib/uap10.0.16299/HarfBuzzSharp.dll", "lib/uap10.0.16299/HarfBuzzSharp.pdb", "lib/uap10.0.16299/HarfBuzzSharp.xml", "lib/xamarinios1.0/HarfBuzzSharp.dll", "lib/xamarinios1.0/HarfBuzzSharp.pdb", "lib/xamarinios1.0/HarfBuzzSharp.xml", "lib/xamarinmac2.0/HarfBuzzSharp.dll", "lib/xamarinmac2.0/HarfBuzzSharp.pdb", "lib/xamarinmac2.0/HarfBuzzSharp.xml", "lib/xamarintvos1.0/HarfBuzzSharp.dll", "lib/xamarintvos1.0/HarfBuzzSharp.pdb", "lib/xamarintvos1.0/HarfBuzzSharp.xml", "lib/xamarinwatchos1.0/HarfBuzzSharp.dll", "lib/xamarinwatchos1.0/HarfBuzzSharp.pdb", "lib/xamarinwatchos1.0/HarfBuzzSharp.xml"]}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"sha512": "UAwIYnkbBTzBJv1Id8FijY/i8QiIepRemSXufU8fyzwWhYJdx4+ajG8yQUie5HW/uusbVLFSr26muSlJOFDgSw==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.7.3.0.3.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"sha512": "RPxRXD16KtSs8Yxr2RK9Qs7AwyN9MlpqZIYs0AvfaJwl7RAtVhC0+u2f2SKwX0uMYYd3O98Z+OBA1sj6aWVKQA==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.7.3.0.3.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "K4os.Compression.LZ4/1.3.8": {"sha512": "LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g==", "type": "package", "path": "k4os.compression.lz4/1.3.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.compression.lz4.1.3.8.nupkg.sha512", "k4os.compression.lz4.nuspec", "lib/net462/K4os.Compression.LZ4.dll", "lib/net462/K4os.Compression.LZ4.xml", "lib/net5.0/K4os.Compression.LZ4.dll", "lib/net5.0/K4os.Compression.LZ4.xml", "lib/net6.0/K4os.Compression.LZ4.dll", "lib/net6.0/K4os.Compression.LZ4.xml", "lib/netstandard2.0/K4os.Compression.LZ4.dll", "lib/netstandard2.0/K4os.Compression.LZ4.xml", "lib/netstandard2.1/K4os.Compression.LZ4.dll", "lib/netstandard2.1/K4os.Compression.LZ4.xml"]}, "K4os.Compression.LZ4.Streams/1.3.8": {"sha512": "P15qr8dZAeo9GvYbUIPEYFQ0MEJ0i5iqr37wsYeRC3la2uCldOoeCa6to0CZ1taiwxIV+Mk8NGuZi+4iWivK9w==", "type": "package", "path": "k4os.compression.lz4.streams/1.3.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.compression.lz4.streams.1.3.8.nupkg.sha512", "k4os.compression.lz4.streams.nuspec", "lib/net462/K4os.Compression.LZ4.Streams.dll", "lib/net462/K4os.Compression.LZ4.Streams.xml", "lib/net5.0/K4os.Compression.LZ4.Streams.dll", "lib/net5.0/K4os.Compression.LZ4.Streams.xml", "lib/net6.0/K4os.Compression.LZ4.Streams.dll", "lib/net6.0/K4os.Compression.LZ4.Streams.xml", "lib/netstandard2.0/K4os.Compression.LZ4.Streams.dll", "lib/netstandard2.0/K4os.Compression.LZ4.Streams.xml", "lib/netstandard2.1/K4os.Compression.LZ4.Streams.dll", "lib/netstandard2.1/K4os.Compression.LZ4.Streams.xml"]}, "K4os.Hash.xxHash/1.0.8": {"sha512": "Wp2F7BamQ2Q/7Hk834nV9vRQapgcr8kgv9Jvfm8J3D0IhDqZMMl+a2yxUq5ltJitvXvQfB8W6K4F4fCbw/P6YQ==", "type": "package", "path": "k4os.hash.xxhash/1.0.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.hash.xxhash.1.0.8.nupkg.sha512", "k4os.hash.xxhash.nuspec", "lib/net462/K4os.Hash.xxHash.dll", "lib/net462/K4os.Hash.xxHash.xml", "lib/net5.0/K4os.Hash.xxHash.dll", "lib/net5.0/K4os.Hash.xxHash.xml", "lib/net6.0/K4os.Hash.xxHash.dll", "lib/net6.0/K4os.Hash.xxHash.xml", "lib/netstandard2.0/K4os.Hash.xxHash.dll", "lib/netstandard2.0/K4os.Hash.xxHash.xml", "lib/netstandard2.1/K4os.Hash.xxHash.dll", "lib/netstandard2.1/K4os.Hash.xxHash.xml"]}, "LiveChartsCore/2.0.0-rc5.4": {"sha512": "1hEEvMndEP5urodx+Epu2lwvJr0ZG246FR7jMYtE9/snYwbUKoItq6a4cTzyeyFPi9fcBsjtgwAWA8uL/2s73g==", "type": "package", "path": "livechartscore/2.0.0-rc5.4", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/LiveChartsCore.dll", "lib/net462/LiveChartsCore.xml", "lib/net6.0-windows10.0.19041/LiveChartsCore.dll", "lib/net6.0-windows10.0.19041/LiveChartsCore.xml", "lib/net6.0-windows7.0/LiveChartsCore.dll", "lib/net6.0-windows7.0/LiveChartsCore.xml", "lib/net8.0-android34.0/LiveChartsCore.dll", "lib/net8.0-android34.0/LiveChartsCore.xml", "lib/net8.0-ios18.0/LiveChartsCore.dll", "lib/net8.0-ios18.0/LiveChartsCore.xml", "lib/net8.0-maccatalyst18.0/LiveChartsCore.dll", "lib/net8.0-maccatalyst18.0/LiveChartsCore.xml", "lib/net8.0-windows10.0.19041/LiveChartsCore.dll", "lib/net8.0-windows10.0.19041/LiveChartsCore.xml", "lib/net8.0-windows7.0/LiveChartsCore.dll", "lib/net8.0-windows7.0/LiveChartsCore.xml", "lib/net8.0/LiveChartsCore.dll", "lib/net8.0/LiveChartsCore.xml", "lib/netstandard2.0/LiveChartsCore.dll", "lib/netstandard2.0/LiveChartsCore.xml", "lib/netstandard2.1/LiveChartsCore.dll", "lib/netstandard2.1/LiveChartsCore.xml", "livechartscore.2.0.0-rc5.4.nupkg.sha512", "livechartscore.nuspec"]}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"sha512": "lpylapUJHvAagM4pmcwCvx3ObfL2FlITtI0u8LkKlLEnzhJYg17Tcxcgd6R/mItQlCdICG0PFDVNUDnZEwhFuw==", "type": "package", "path": "livechartscore.skiasharpview/2.0.0-rc5.4", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/LiveChartsCore.SkiaSharpView.dll", "lib/net462/LiveChartsCore.SkiaSharpView.xml", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.dll", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-android34.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-android34.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-ios18.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-ios18.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-maccatalyst18.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-maccatalyst18.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-windows10.0.20348/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-windows10.0.20348/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0/LiveChartsCore.SkiaSharpView.xml", "lib/netstandard2.0/LiveChartsCore.SkiaSharpView.dll", "lib/netstandard2.0/LiveChartsCore.SkiaSharpView.xml", "lib/netstandard2.1/LiveChartsCore.SkiaSharpView.dll", "lib/netstandard2.1/LiveChartsCore.SkiaSharpView.xml", "livechartscore.skiasharpview.2.0.0-rc5.4.nupkg.sha512", "livechartscore.skiasharpview.nuspec"]}, "LiveChartsCore.SkiaSharpView.WinForms/2.0.0-rc5.4": {"sha512": "620/ZcHt4gaDfeAjKd/dWc9TJvBwku+A5Go8dbEKyAZCCW1x4o2IpNf2RvnYw3El6aDCcPtQMd9YIEw8fEhS2g==", "type": "package", "path": "livechartscore.skiasharpview.winforms/2.0.0-rc5.4", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/LiveChartsCore.SkiaSharpView.WinForms.dll", "lib/net462/LiveChartsCore.SkiaSharpView.WinForms.xml", "lib/net6.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WinForms.dll", "lib/net6.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WinForms.xml", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.WinForms.dll", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.WinForms.xml", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WinForms.dll", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WinForms.xml", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WinForms.dll", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WinForms.xml", "livechartscore.skiasharpview.winforms.2.0.0-rc5.4.nupkg.sha512", "livechartscore.skiasharpview.winforms.nuspec"]}, "MathNet.Numerics.Signed/5.0.0": {"sha512": "PSrHBVMf41SjbhlnpOMnoir8YgkyEJ6/nwxvjYpH+vJCexNcx2ms6zRww5yLVqLet1xLJgZ39swtKRTLhWdnAw==", "type": "package", "path": "mathnet.numerics.signed/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/MathNet.Numerics.dll", "lib/net461/MathNet.Numerics.xml", "lib/net48/MathNet.Numerics.dll", "lib/net48/MathNet.Numerics.xml", "lib/net5.0/MathNet.Numerics.dll", "lib/net5.0/MathNet.Numerics.xml", "lib/net6.0/MathNet.Numerics.dll", "lib/net6.0/MathNet.Numerics.xml", "lib/netstandard2.0/MathNet.Numerics.dll", "lib/netstandard2.0/MathNet.Numerics.xml", "mathnet.numerics.signed.5.0.0.nupkg.sha512", "mathnet.numerics.signed.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"sha512": "irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "type": "package", "path": "microsoft.io.recyclablememorystream/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "MySql.Data/9.3.0": {"sha512": "f4iZBKubzwNnc84ecEwguv5TSwHpoArFGa+XpZY+8S9SoAnQaHfymLdZUKK4LbUbH9ACZ6X71LUvq58Mfdyipw==", "type": "package", "path": "mysql.data/9.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README", "README.md", "lib/net462/MySql.Data.dll", "lib/net462/MySql.Data.xml", "lib/net48/MySql.Data.dll", "lib/net48/MySql.Data.xml", "lib/net8.0/MySql.Data.dll", "lib/net8.0/MySql.Data.xml", "lib/net9.0/MySql.Data.dll", "lib/net9.0/MySql.Data.xml", "lib/netstandard2.0/MySql.Data.dll", "lib/netstandard2.0/MySql.Data.xml", "lib/netstandard2.1/MySql.Data.dll", "lib/netstandard2.1/MySql.Data.xml", "logo-mysql-170x115.png", "mysql.data.9.3.0.nupkg.sha512", "mysql.data.nuspec", "runtimes/win-x64/native/comerr64.dll", "runtimes/win-x64/native/gssapi64.dll", "runtimes/win-x64/native/k5sprt64.dll", "runtimes/win-x64/native/krb5_64.dll", "runtimes/win-x64/native/krbcc64.dll"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NPOI/2.7.3": {"sha512": "iCZx3DSwUSwaV61E8tXgPlPuxYmcYV/Zi405nGlxQvWaGTAbuc0KvSBjsLucQUJ92iMeetT8iK9makLfF4uZ3g==", "type": "package", "path": "npoi/2.7.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net472/NPOI.Core.dll", "lib/net472/NPOI.Core.pdb", "lib/net472/NPOI.Core.xml", "lib/net472/NPOI.OOXML.dll", "lib/net472/NPOI.OOXML.pdb", "lib/net472/NPOI.OOXML.xml", "lib/net472/NPOI.OpenXml4Net.dll", "lib/net472/NPOI.OpenXml4Net.pdb", "lib/net472/NPOI.OpenXml4Net.xml", "lib/net472/NPOI.OpenXmlFormats.dll", "lib/net472/NPOI.OpenXmlFormats.pdb", "lib/net472/NPOI.OpenXmlFormats.xml", "lib/net8.0/NPOI.Core.dll", "lib/net8.0/NPOI.Core.pdb", "lib/net8.0/NPOI.Core.xml", "lib/net8.0/NPOI.OOXML.dll", "lib/net8.0/NPOI.OOXML.pdb", "lib/net8.0/NPOI.OOXML.xml", "lib/net8.0/NPOI.OpenXml4Net.dll", "lib/net8.0/NPOI.OpenXml4Net.pdb", "lib/net8.0/NPOI.OpenXml4Net.xml", "lib/net8.0/NPOI.OpenXmlFormats.dll", "lib/net8.0/NPOI.OpenXmlFormats.pdb", "lib/net8.0/NPOI.OpenXmlFormats.xml", "lib/netstandard2.0/NPOI.Core.dll", "lib/netstandard2.0/NPOI.Core.pdb", "lib/netstandard2.0/NPOI.Core.xml", "lib/netstandard2.0/NPOI.OOXML.dll", "lib/netstandard2.0/NPOI.OOXML.pdb", "lib/netstandard2.0/NPOI.OOXML.xml", "lib/netstandard2.0/NPOI.OpenXml4Net.dll", "lib/netstandard2.0/NPOI.OpenXml4Net.pdb", "lib/netstandard2.0/NPOI.OpenXml4Net.xml", "lib/netstandard2.0/NPOI.OpenXmlFormats.dll", "lib/netstandard2.0/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.0/NPOI.OpenXmlFormats.xml", "lib/netstandard2.1/NPOI.Core.dll", "lib/netstandard2.1/NPOI.Core.pdb", "lib/netstandard2.1/NPOI.Core.xml", "lib/netstandard2.1/NPOI.OOXML.dll", "lib/netstandard2.1/NPOI.OOXML.pdb", "lib/netstandard2.1/NPOI.OOXML.xml", "lib/netstandard2.1/NPOI.OpenXml4Net.dll", "lib/netstandard2.1/NPOI.OpenXml4Net.pdb", "lib/netstandard2.1/NPOI.OpenXml4Net.xml", "lib/netstandard2.1/NPOI.OpenXmlFormats.dll", "lib/netstandard2.1/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.1/NPOI.OpenXmlFormats.xml", "logo/120_120.jpg", "logo/240_240.png", "logo/32_32.jpg", "logo/60_60.jpg", "npoi.2.7.3.nupkg.sha512", "npoi.nuspec"]}, "OpenTK/3.1.0": {"sha512": "xoP5kb81lRSY5RzbntaFA9yXK+aGF5SLShhns55MyZtxtNafIxRH5KvRTFss/N653brTzE+AfepjKsbMexYlBQ==", "type": "package", "path": "opentk/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "content/OpenTK.dll.config", "lib/net20/OpenTK.dll", "lib/net20/OpenTK.pdb", "lib/net20/OpenTK.xml", "opentk.3.1.0.nupkg.sha512", "opentk.nuspec"]}, "OpenTK.GLControl/3.1.0": {"sha512": "BjDzF+MhJd2LaZYzkXmgkzzXgHAko/Iur4rGy6+DPdZA1Jcc0X6jXZ6QsUsjlLRNzgevO7uAj2h1TqX7sguaYg==", "type": "package", "path": "opentk.glcontrol/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/OpenTK.GLControl.dll", "lib/net20/OpenTK.GLControl.pdb", "lib/net20/OpenTK.GLControl.xml", "opentk.glcontrol.3.1.0.nupkg.sha512", "opentk.glcontrol.nuspec"]}, "SharpZipLib/1.4.2": {"sha512": "yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "type": "package", "path": "sharpziplib/1.4.2", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net6.0/ICSharpCode.SharpZipLib.dll", "lib/net6.0/ICSharpCode.SharpZipLib.pdb", "lib/net6.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.4.2.nupkg.sha512", "sharpziplib.nuspec"]}, "SixLabors.Fonts/1.0.1": {"sha512": "ljezRHWc7N0azdQViib7Aa5v+DagRVkKI2/93kEbtjVczLs+yTkSq6gtGmvOcx4IqyNbO3GjLt7SAQTpLkySNw==", "type": "package", "path": "sixlabors.fonts/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.1.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/2.1.10": {"sha512": "hk1E7U3RSlxrBVo6Gb6OjeM52fChpFYH+SZvyT/M2vzSGlzAaKE33hc3V/Pvnjcnn1opT8/Z+0QfqdM5HsIaeA==", "type": "package", "path": "sixlabors.imagesharp/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/SixLabors.ImageSharp.dll", "lib/net472/SixLabors.ImageSharp.xml", "lib/netcoreapp2.1/SixLabors.ImageSharp.dll", "lib/netcoreapp2.1/SixLabors.ImageSharp.xml", "lib/netcoreapp3.1/SixLabors.ImageSharp.dll", "lib/netcoreapp3.1/SixLabors.ImageSharp.xml", "lib/netstandard2.0/SixLabors.ImageSharp.dll", "lib/netstandard2.0/SixLabors.ImageSharp.xml", "lib/netstandard2.1/SixLabors.ImageSharp.dll", "lib/netstandard2.1/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.2.1.10.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SkiaSharp/2.88.9": {"sha512": "3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "type": "package", "path": "skiasharp/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.9.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.HarfBuzz/2.88.9": {"sha512": "5lyjX04CSWTKFYMSNN6SMupV2qzYDr8W/u8S3ZEkr+Sg3kOi0YE06EOqG0tzn/YPBID89xam4L3rz3hUUKOGEQ==", "type": "package", "path": "skiasharp.harfbuzz/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.HarfBuzz.dll", "lib/net462/SkiaSharp.HarfBuzz.pdb", "lib/net462/SkiaSharp.HarfBuzz.xml", "lib/net6.0/SkiaSharp.HarfBuzz.dll", "lib/net6.0/SkiaSharp.HarfBuzz.pdb", "lib/net6.0/SkiaSharp.HarfBuzz.xml", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.dll", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.pdb", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.xml", "lib/netstandard1.3/SkiaSharp.HarfBuzz.dll", "lib/netstandard1.3/SkiaSharp.HarfBuzz.pdb", "lib/netstandard1.3/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.0/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.0/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.0/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.1/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.1/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.1/SkiaSharp.HarfBuzz.xml", "skiasharp.harfbuzz.2.88.9.nupkg.sha512", "skiasharp.harfbuzz.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"sha512": "Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"sha512": "wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "SkiaSharp.Views.Desktop.Common/2.88.8": {"sha512": "SBP7byk9a5gmzHriOTC6BjykqJ5rCTqXIo/RLNszPaKwDs8naCZGUM9GZmreshPkpsyez3Ci4sm3+eleuSGgVQ==", "type": "package", "path": "skiasharp.views.desktop.common/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.Views.Desktop.Common.dll", "lib/net462/SkiaSharp.Views.Desktop.Common.pdb", "lib/net462/SkiaSharp.Views.Desktop.Common.xml", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.pdb", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.xml", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.dll", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.pdb", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.xml", "skiasharp.views.desktop.common.2.88.8.nupkg.sha512", "skiasharp.views.desktop.common.nuspec"]}, "SkiaSharp.Views.WindowsForms/2.88.8": {"sha512": "AEy9unq+DIzV6EqM7pJ+UuOo+Qdr2EsASggJEdtxTImFDj4jFUZ3bjTSre65hD+UGnX2QFn/hy2X8SW+v6bR8A==", "type": "package", "path": "skiasharp.views.windowsforms/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.Views.WindowsForms.dll", "lib/net462/SkiaSharp.Views.WindowsForms.pdb", "lib/net462/SkiaSharp.Views.WindowsForms.xml", "lib/netcoreapp3.1/SkiaSharp.Views.WindowsForms.dll", "lib/netcoreapp3.1/SkiaSharp.Views.WindowsForms.pdb", "lib/netcoreapp3.1/SkiaSharp.Views.WindowsForms.xml", "skiasharp.views.windowsforms.2.88.8.nupkg.sha512", "skiasharp.views.windowsforms.nuspec"]}, "SqlSugar/5.1.4.195": {"sha512": "I8UTddKmYE13y+pbmigsxZ4KdSzHnRW53uT+MTgxax6zIfyvNDWFoy4cOnDlzo8+KWCBsRNORhfKcBUWZZGBQg==", "type": "package", "path": "sqlsugar/5.1.4.195", "files": [".nupkg.metadata", ".signature.p7s", "lib/SqlSugar.dll", "sqlsugar.5.1.4.195.nupkg.sha512", "sqlsugar.nuspec"]}, "Stub.System.Data.SQLite.Core.NetFramework/1.0.119": {"sha512": "8b4SbSXAxXJ8tfn6bnBu0m+HXMZvkE+BfogUKITRFm+snxfT5BTK7fLZaAsNoFX8DkBBjtCEDd+ajWkcyu55QA==", "type": "package", "path": "stub.system.data.sqlite.core.netframework/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "build/net20/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net20/x64/SQLite.Interop.dll", "build/net20/x86/SQLite.Interop.dll", "build/net40/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net40/x64/SQLite.Interop.dll", "build/net40/x86/SQLite.Interop.dll", "build/net45/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net45/x64/SQLite.Interop.dll", "build/net45/x86/SQLite.Interop.dll", "build/net451/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net451/x64/SQLite.Interop.dll", "build/net451/x86/SQLite.Interop.dll", "build/net46/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net46/x64/SQLite.Interop.dll", "build/net46/x86/SQLite.Interop.dll", "buildTransitive/net20/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net40/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net45/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net451/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets", "lib/net20/System.Data.SQLite.dll", "lib/net20/System.Data.SQLite.dll.altconfig", "lib/net20/System.Data.SQLite.xml", "lib/net40/System.Data.SQLite.dll", "lib/net40/System.Data.SQLite.dll.altconfig", "lib/net40/System.Data.SQLite.xml", "lib/net45/System.Data.SQLite.dll", "lib/net45/System.Data.SQLite.dll.altconfig", "lib/net45/System.Data.SQLite.xml", "lib/net451/System.Data.SQLite.dll", "lib/net451/System.Data.SQLite.dll.altconfig", "lib/net451/System.Data.SQLite.xml", "lib/net46/System.Data.SQLite.dll", "lib/net46/System.Data.SQLite.dll.altconfig", "lib/net46/System.Data.SQLite.xml", "stub.system.data.sqlite.core.netframework.1.0.119.nupkg.sha512", "stub.system.data.sqlite.core.netframework.nuspec"]}, "SunnyUI/3.8.2": {"sha512": "CnZ1qpuQtY14jF2RXwS219wPbJFeZHarrEy04p2+oYQL4SeeYpMRwmMnBSUehRAj1cR5WCNHxUF/bH7tCacz7g==", "type": "package", "path": "sunnyui/3.8.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.dll", "lib/net472/SunnyUI.dll", "lib/net8.0-windows7.0/SunnyUI.dll", "lib/net9.0-windows7.0/SunnyUI.dll", "sunnyui.3.8.2.nupkg.sha512", "sunnyui.nuspec"]}, "SunnyUI.Common/3.8.2": {"sha512": "OIz2kvCX3HLYPJLilUV7rnK34tyg3DDwz+O0omGIE1tIBoOlrkZllmTWhQz7DbwzECefyCCW/4rPB088EiMocQ==", "type": "package", "path": "sunnyui.common/3.8.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.Common.dll", "lib/net472/SunnyUI.Common.dll", "lib/net8.0/SunnyUI.Common.dll", "lib/net9.0/SunnyUI.Common.dll", "sunnyui.common.3.8.2.nupkg.sha512", "sunnyui.common.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/8.0.0": {"sha512": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "type": "package", "path": "system.configuration.configurationmanager/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SQLite/1.0.119": {"sha512": "JSOJpnBf9goMnxGTJFGCmm6AffxgtpuXNXV5YvWO8UNC2zwd12qkUe5lAbnY+2ohIkIukgIjbvR1RA/sWILv3w==", "type": "package", "path": "system.data.sqlite/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.1.0.119.nupkg.sha512", "system.data.sqlite.nuspec"]}, "System.Data.SQLite.Core/1.0.119": {"sha512": "bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "type": "package", "path": "system.data.sqlite.core/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.core.1.0.119.nupkg.sha512", "system.data.sqlite.core.nuspec"]}, "System.Data.SQLite.EF6/1.0.119": {"sha512": "BwwgCSeA80gsxdXtU7IQEBrN9kQXWQrD11hNYOJZbXBBI1C4r7hA4QhBAalO1nzijXikthGRUADIEMI3nlucLA==", "type": "package", "path": "system.data.sqlite.ef6/1.0.119", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "content/net40/app.config.install.xdt", "content/net40/app.config.transform", "content/net40/web.config.install.xdt", "content/net40/web.config.transform", "content/net45/app.config.install.xdt", "content/net45/app.config.transform", "content/net45/web.config.install.xdt", "content/net45/web.config.transform", "content/net451/app.config.install.xdt", "content/net451/app.config.transform", "content/net451/web.config.install.xdt", "content/net451/web.config.transform", "content/net46/app.config.install.xdt", "content/net46/app.config.transform", "content/net46/web.config.install.xdt", "content/net46/web.config.transform", "lib/net40/System.Data.SQLite.EF6.dll", "lib/net45/System.Data.SQLite.EF6.dll", "lib/net451/System.Data.SQLite.EF6.dll", "lib/net46/System.Data.SQLite.EF6.dll", "lib/netstandard2.1/System.Data.SQLite.EF6.dll", "system.data.sqlite.ef6.1.0.119.nupkg.sha512", "system.data.sqlite.ef6.nuspec", "tools/net40/install.ps1", "tools/net45/install.ps1", "tools/net451/install.ps1", "tools/net46/install.ps1"]}, "System.Data.SQLite.Linq/1.0.119": {"sha512": "tkzb9aKEiQd18UW8VS6+vGtrkhcp4mjHg/AAKwNN8u7aWoBswquOnU07016MfTXyBUAR67mepWUhFC8zqNdPnA==", "type": "package", "path": "system.data.sqlite.linq/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "content/net20/app.config.transform", "content/net20/web.config.transform", "content/net40/app.config.transform", "content/net40/web.config.transform", "content/net45/app.config.transform", "content/net45/web.config.transform", "content/net451/app.config.transform", "content/net451/web.config.transform", "content/net46/app.config.transform", "content/net46/web.config.transform", "lib/net20/System.Data.SQLite.Linq.dll", "lib/net40/System.Data.SQLite.Linq.dll", "lib/net45/System.Data.SQLite.Linq.dll", "lib/net451/System.Data.SQLite.Linq.dll", "lib/net46/System.Data.SQLite.Linq.dll", "system.data.sqlite.linq.1.0.119.nupkg.sha512", "system.data.sqlite.linq.nuspec"]}, "System.Diagnostics.DiagnosticSource/8.0.1": {"sha512": "vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/4.7.3": {"sha512": "B3+wwhAeoUQ6KeshWSq3IRLQiMoqPEzSHzyVyxTI/EbYuqIp90lXrRISlip2AF+5tj74ycIVPpnRY4M424HahA==", "type": "package", "path": "system.drawing.common/4.7.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.xml", "ref/netstandard2.0/System.Drawing.Common.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.xml", "system.drawing.common.4.7.3.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Pipelines/5.0.2": {"sha512": "Iew+dfa6FFiyvWBdRmXApixRY1db+beyutpIck4SOSe0NLM8FD/7AD54MscqVLhvfSMLHO7KadjTRT7fqxOGTA==", "type": "package", "path": "system.io.pipelines/5.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/netcoreapp3.0/System.IO.Pipelines.dll", "lib/netcoreapp3.0/System.IO.Pipelines.xml", "lib/netstandard1.3/System.IO.Pipelines.dll", "lib/netstandard1.3/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "ref/netcoreapp2.0/System.IO.Pipelines.dll", "ref/netcoreapp2.0/System.IO.Pipelines.xml", "system.io.pipelines.5.0.2.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/8.0.2": {"sha512": "aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "type": "package", "path": "system.security.cryptography.xml/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/net7.0/System.Security.Cryptography.Xml.dll", "lib/net7.0/System.Security.Cryptography.Xml.xml", "lib/net8.0/System.Security.Cryptography.Xml.dll", "lib/net8.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.8.0.2.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/5.0.0": {"sha512": "NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "type": "package", "path": "system.text.encoding.codepages/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.5.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "ZstdSharp.Port/0.8.5": {"sha512": "TR4j17WeVSEb3ncgL2NqlXEqcy04I+Kk9CaebNDplUeL8XOgjkZ7fP4Wg4grBdPLIqsV86p2QaXTkZoRMVOsew==", "type": "package", "path": "zstdsharp.port/0.8.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/net8.0/ZstdSharp.dll", "lib/net9.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.8.5.nupkg.sha512", "zstdsharp.port.nuspec"]}, "LibBaseModules/1.0.0": {"type": "project", "path": "../LibBaseModules/LibBaseModules.csproj", "msbuildProject": "../LibBaseModules/LibBaseModules.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["LibBaseModules >= 1.0.0", "LiveChartsCore >= 2.0.0-rc5.4", "LiveChartsCore.SkiaSharpView.WinForms >= 2.0.0-rc5.4", "MySql.Data >= 9.3.0", "NPOI >= 2.7.3", "Newtonsoft.Json >= 13.0.3", "SkiaSharp.Views.WindowsForms >= 2.88.8", "SqlSugar >= 5.1.4.195", "SunnyUI >= 3.8.2", "System.Data.SQLite >= 1.0.119"]}, "packageFolders": {"F:\\Nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\LibBusinessModules.csproj", "projectName": "LibBusinessModules", "projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\LibBusinessModules.csproj", "packagesPath": "F:\\Nuget\\packages\\", "outputPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://pcnuget.fpi-inc.com/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {"G:\\01-MyCode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj": {"projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"LiveChartsCore": {"target": "Package", "version": "[2.0.0-rc5.4, )"}, "LiveChartsCore.SkiaSharpView.WinForms": {"target": "Package", "version": "[2.0.0-rc5.4, )"}, "MySql.Data": {"target": "Package", "version": "[9.3.0, )"}, "NPOI": {"target": "Package", "version": "[2.7.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SkiaSharp.Views.WindowsForms": {"target": "Package", "version": "[2.88.8, )"}, "SqlSugar": {"target": "Package", "version": "[5.1.4.195, )"}, "SunnyUI": {"target": "Package", "version": "[3.8.2, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.119, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "logs": [{"code": "NU1605", "level": "Warning", "warningLevel": 1, "message": "检测到包降级: SkiaSharp.Views.WindowsForms 从 2.88.9 降级到 2.88.8。直接从项目引用包以选择不同版本。 \r\n LibBusinessModules -> LiveChartsCore.SkiaSharpView.WinForms 2.0.0-rc5.4 -> SkiaSharp.Views.WindowsForms (>= 2.88.9) \r\n LibBusinessModules -> SkiaSharp.Views.WindowsForms (>= 2.88.8)", "libraryId": "SkiaSharp.Views.WindowsForms", "targetGraphs": [".NETFramework,Version=v4.8", ".NETFramework,Version=v4.8/win", ".NETFramework,Version=v4.8/win-arm64", ".NETFramework,Version=v4.8/win-x64", ".NETFramework,Version=v4.8/win-x86"]}]}