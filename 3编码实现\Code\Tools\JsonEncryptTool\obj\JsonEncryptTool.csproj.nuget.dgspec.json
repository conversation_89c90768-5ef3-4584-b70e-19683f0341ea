{"format": 1, "restore": {"G:\\01-MyCode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonEncryptTool\\JsonEncryptTool.csproj": {}}, "projects": {"G:\\01-MyCode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonEncryptTool\\JsonEncryptTool.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonEncryptTool\\JsonEncryptTool.csproj", "projectName": "配置文件加密工具", "projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonEncryptTool\\JsonEncryptTool.csproj", "packagesPath": "F:\\Nuget\\packages\\", "outputPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonEncryptTool\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net20"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://pcnuget.fpi-inc.com/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net20": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net20": {}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}