1.  登陆软件，默认密码fahb123，可在系统配置中修改。

2.  系统配置，因子信息管理，依照各因子实际情况，完成因子基本信息配置。

![](./images/media/image1.png){width="5.768055555555556in"
height="3.9743055555555555in"}

3.  本地Templates文件夹下，修改报表模板。

> 各类模板的Default.docx文件，对应没有找到 ID识别码
> 的设备所用模板。若需要针对特定因子修改模板，需要复制Default.docx进行修改。修改完成后请命名为
> FA0101.docx 这种。修改过程中，{{xxxx}} 这种替换标识符不可修改。

![](./images/media/image2.png){width="5.768055555555556in"
height="2.7534722222222223in"}

4.  数据提取，提取数据。本地提取或FTP提取均可。FTP默认信息可在系统配置中修改。也可在提取页面临时调整（不会保存）。

![](./images/media/image3.png){width="5.768055555555556in"
height="3.2444444444444445in"}

5.  报表导出界面，选择要到处设备的SN码。支持模糊搜索。点击查询，提取数据。下方数据表中，勾选数据导出需要用到的数据。勾选错误会有提示。导出成功后，提示打开导出文件所在目录。默认导出位置为\\
    Reports\\因子类型\\SN号文件夹。三种报表都放在其中。报表样式由模板指定。

![](./images/media/image4.png){width="5.768055555555556in"
height="3.2444444444444445in"}

![](./images/media/image5.png){width="5.768055555555556in"
height="3.2444444444444445in"}

![](./images/media/image6.png){width="5.768055555555556in"
height="2.879861111111111in"}

![](./images/media/image7.png){width="5.768055555555556in"
height="1.7597222222222222in"}

6.  报表中心界面，可以按年、月、周、日为周期查询指定时段内的测试情况。还可以导出测试报告。

![](./images/media/image8.png){width="5.768055555555556in"
height="3.2472222222222222in"}

7.  故障统计界面，可以查询指点时段的故障信息。导出图片格式的统计报告。

![](./images/media/image9.png){width="5.768055555555556in"
height="3.2472222222222222in"}

8.  故障记录界面，可以查询指定时段的故障记录，导出为excel表格。还可以手动新增（按钮点击弹出界面）、删除（表格中右键选择删除）故障记录。

![](./images/media/image10.png){width="5.768055555555556in"
height="3.2263888888888888in"}

9.  通信测试界面，可以进行串口通信调试。

![](./images/media/image11.png){width="5.768055555555556in"
height="3.222916666666667in"}

10. 故障码管理界面，可以管理一级、二级故障类型。

![](./images/media/image12.png){width="5.768055555555556in"
height="3.5805555555555557in"}

注意：

1.  当前软件默认使用sqlite数据库。如果要提升查询性能，建议安装mysql8.0版本的数据库，并在系统设置-数据库配置中进行切换。

2.  本版本软件自带数据库中有部分5-6日至6-6日之间的模拟数据，供测试软件功能使用。可以通过删除DB文件夹，然后重启软件的形式来去除这部分数据。

3.  老版本软件有一张表较新版缺失部分数据。如果有当前在使用的老软件的历史数据想保存，可以把数据库文件发我来手动转换升级，再放到新软件DB目录即可。
